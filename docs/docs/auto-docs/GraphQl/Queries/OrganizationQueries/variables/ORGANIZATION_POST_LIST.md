[Admin Docs](/)

***

# Variable: ORGANIZATION\_POST\_LIST

> `const` **ORGANIZATION\_POST\_LIST**: `DocumentNode`

Defined in: [GraphQl/Queries/OrganizationQueries.ts:16](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/GraphQl/Queries/OrganizationQueries.ts#L16)

GraphQL query to retrieve the list of organizations.

## Param

Optional. Number of organizations to retrieve in the first batch.

## Param

Optional. Number of organizations to skip before starting to collect the result set.

## Param

Optional. Filter organizations by a specified string.

## Param

Optional. The ID of a specific organization to retrieve.

## Returns

The list of organizations based on the applied filters.
