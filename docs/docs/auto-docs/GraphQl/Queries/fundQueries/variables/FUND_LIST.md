[Admin Docs](/)

***

# Variable: FUND\_LIST

> `const` **FUND\_LIST**: `DocumentNode`

Defined in: [GraphQl/Queries/fundQueries.ts:13](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/GraphQl/Queries/fundQueries.ts#L13)

GraphQL query to retrieve the list of members for a specific organization.

## Param

The ID of the organization for which members are being retrieved.

## Param

The name of the organization for which members are being retrieved.

## Param

The ID of the creator of the organization.

## Param

The ID of the user who last updated the organization.

## Param

A boolean value indicating whether the organization is tax deductible.

## Returns

The list of members associated with the organization.
