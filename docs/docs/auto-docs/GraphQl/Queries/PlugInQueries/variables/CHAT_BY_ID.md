[Admin Docs](/)

***

# Variable: CHAT\_BY\_ID

> `const` **CHAT\_BY\_ID**: `DocumentNode`

Defined in: [GraphQl/Queries/PlugInQueries.ts:138](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/GraphQl/Queries/PlugInQueries.ts#L138)

GraphQL query to retrieve a list of chats based on user ID.

## Param

The ID of the user for which chats are being retrieved.

## Returns

The list of chats associated with the user, including details such as ID, creator, messages, organization, and participating users.
