[Admin Docs](/)

***

# Variable: IS\_SAMPLE\_ORGANIZATION\_QUERY

> `const` **IS\_SAMPLE\_ORGANIZATION\_QUERY**: `DocumentNode`

Defined in: [GraphQl/Queries/PlugInQueries.ts:349](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/GraphQl/Queries/PlugInQueries.ts#L349)

GraphQL query to check if an organization is a sample organization.

## Param

The ID of the organization being checked.

## Returns

A boolean indicating whether the organization is a sample organization.
