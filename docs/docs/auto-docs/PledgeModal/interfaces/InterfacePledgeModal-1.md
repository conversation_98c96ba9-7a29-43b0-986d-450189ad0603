[Admin Docs](/)

***

# Interface: InterfacePledgeModal

Defined in: [screens/UserPortal/Campaigns/PledgeModal.tsx:68](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/UserPortal/Campaigns/PledgeModal.tsx#L68)

## Properties

### campaignId

> **campaignId**: `string`

Defined in: [screens/UserPortal/Campaigns/PledgeModal.tsx:71](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/UserPortal/Campaigns/PledgeModal.tsx#L71)

***

### endDate

> **endDate**: `Date`

Defined in: [screens/UserPortal/Campaigns/PledgeModal.tsx:75](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/UserPortal/Campaigns/PledgeModal.tsx#L75)

***

### hide()

> **hide**: () => `void`

Defined in: [screens/UserPortal/Campaigns/PledgeModal.tsx:70](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/UserPortal/Campaigns/PledgeModal.tsx#L70)

#### Returns

`void`

***

### isOpen

> **isOpen**: `boolean`

Defined in: [screens/UserPortal/Campaigns/PledgeModal.tsx:69](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/UserPortal/Campaigns/PledgeModal.tsx#L69)

***

### mode

> **mode**: `"create"` \| `"edit"`

Defined in: [screens/UserPortal/Campaigns/PledgeModal.tsx:76](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/UserPortal/Campaigns/PledgeModal.tsx#L76)

***

### pledge

> **pledge**: [`InterfacePledgeInfo`](../../utils/interfaces/interfaces/InterfacePledgeInfo.md)

Defined in: [screens/UserPortal/Campaigns/PledgeModal.tsx:73](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/UserPortal/Campaigns/PledgeModal.tsx#L73)

***

### refetchPledge()

> **refetchPledge**: () => `void`

Defined in: [screens/UserPortal/Campaigns/PledgeModal.tsx:74](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/UserPortal/Campaigns/PledgeModal.tsx#L74)

#### Returns

`void`

***

### userId

> **userId**: `string`

Defined in: [screens/UserPortal/Campaigns/PledgeModal.tsx:72](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/UserPortal/Campaigns/PledgeModal.tsx#L72)
