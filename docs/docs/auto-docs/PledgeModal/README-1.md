[Admin Docs](/)

***

# PledgeModal

## File

PledgeModal.tsx

## Description

This file defines the `PledgeModal` component, which provides a modal interface for creating or editing pledges
             in a campaign. It includes form fields for selecting pledgers, specifying pledge amounts, currencies, and dates.
             The component supports internationalization and integrates with GraphQL mutations and queries for data handling.

## Interfaces

- [InterfacePledgeModal](interfaces/InterfacePledgeModal-1.md)

## Variables

- [default](variables/default-1.md)
