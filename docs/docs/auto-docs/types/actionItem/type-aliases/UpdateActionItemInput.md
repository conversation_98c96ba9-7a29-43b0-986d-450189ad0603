[Admin Docs](/)

***

# Type Alias: UpdateActionItemInput

> **UpdateActionItemInput** = `object`

Defined in: [types/actionItem.ts:39](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L39)

## Properties

### assigneeId?

> `optional` **assigneeId**: `string`

Defined in: [types/actionItem.ts:40](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L40)

***

### completionDate?

> `optional` **completionDate**: `Date`

Defined in: [types/actionItem.ts:41](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L41)

***

### dueDate?

> `optional` **dueDate**: `Date`

Defined in: [types/actionItem.ts:42](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L42)

***

### isCompleted?

> `optional` **isCompleted**: `boolean`

Defined in: [types/actionItem.ts:43](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L43)

***

### postCompletionNotes?

> `optional` **postCompletionNotes**: `string`

Defined in: [types/actionItem.ts:44](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L44)

***

### preCompletionNotes?

> `optional` **preCompletionNotes**: `string`

Defined in: [types/actionItem.ts:45](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L45)
