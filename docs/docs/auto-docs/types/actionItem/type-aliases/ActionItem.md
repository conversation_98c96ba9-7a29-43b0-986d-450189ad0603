[Admin Docs](/)

***

# Type Alias: ActionItem

> **ActionItem** = `object`

Defined in: [types/actionItem.ts:5](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L5)

## Properties

### \_id

> **\_id**: `string`

Defined in: [types/actionItem.ts:6](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L6)

***

### actionItemCategory?

> `optional` **actionItemCategory**: [`ActionItemCategory`](ActionItemCategory.md)

Defined in: [types/actionItem.ts:7](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L7)

***

### assignee?

> `optional` **assignee**: [`User`](../../User/type/type-aliases/User.md)

Defined in: [types/actionItem.ts:8](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L8)

***

### assigner?

> `optional` **assigner**: [`User`](../../User/type/type-aliases/User.md)

Defined in: [types/actionItem.ts:9](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L9)

***

### assignmentDate

> **assignmentDate**: `Date`

Defined in: [types/actionItem.ts:10](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L10)

***

### completionDate

> **completionDate**: `Date`

Defined in: [types/actionItem.ts:11](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L11)

***

### createdAt

> **createdAt**: `Date`

Defined in: [types/actionItem.ts:12](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L12)

***

### creator?

> `optional` **creator**: [`User`](../../User/type/type-aliases/User.md)

Defined in: [types/actionItem.ts:13](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L13)

***

### dueDate

> **dueDate**: `Date`

Defined in: [types/actionItem.ts:14](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L14)

***

### event?

> `optional` **event**: `Event`

Defined in: [types/actionItem.ts:15](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L15)

***

### isCompleted

> **isCompleted**: `boolean`

Defined in: [types/actionItem.ts:16](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L16)

***

### postCompletionNotes?

> `optional` **postCompletionNotes**: `string`

Defined in: [types/actionItem.ts:17](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L17)

***

### preCompletionNotes?

> `optional` **preCompletionNotes**: `string`

Defined in: [types/actionItem.ts:18](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L18)

***

### updatedAt

> **updatedAt**: `Date`

Defined in: [types/actionItem.ts:19](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L19)
