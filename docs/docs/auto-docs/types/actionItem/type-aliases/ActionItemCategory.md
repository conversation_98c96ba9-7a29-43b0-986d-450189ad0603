[Admin Docs](/)

***

# Type Alias: ActionItemCategory

> **ActionItemCategory** = `object`

Defined in: [types/actionItem.ts:22](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L22)

## Properties

### \_id

> **\_id**: `string`

Defined in: [types/actionItem.ts:23](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L23)

***

### createdAt

> **createdAt**: `Date`

Defined in: [types/actionItem.ts:24](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L24)

***

### creator?

> `optional` **creator**: [`User`](../../User/type/type-aliases/User.md)

Defined in: [types/actionItem.ts:25](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L25)

***

### isDisabled

> **isDisabled**: `boolean`

Defined in: [types/actionItem.ts:26](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L26)

***

### name

> **name**: `string`

Defined in: [types/actionItem.ts:27](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L27)

***

### organization?

> `optional` **organization**: [`Organization`](../../Organization/type/type-aliases/Organization.md)

Defined in: [types/actionItem.ts:28](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L28)

***

### updatedAt

> **updatedAt**: `Date`

Defined in: [types/actionItem.ts:29](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/types/actionItem.ts#L29)
