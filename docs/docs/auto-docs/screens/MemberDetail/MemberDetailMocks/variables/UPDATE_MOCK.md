[Admin Docs](/)

***

# Variable: UPDATE\_MOCK

> `const` **UPDATE\_MOCK**: `object`[]

Defined in: [screens/MemberDetail/MemberDetailMocks.ts:149](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/MemberDetail/MemberDetailMocks.ts#L149)

## Type declaration

### request

> **request**: `object`

#### request.query

> **query**: `DocumentNode` = `UPDATE_CURRENT_USER_MUTATION`

#### request.variables

> **variables**: `object`

#### request.variables.input

> **input**: `object`

#### request.variables.input.name

> **name**: `string` = `'Bandhan Majumder'`

### result

> **result**: `object`

#### result.data

> **data**: `object`

#### result.data.updateCurrentUser

> **updateCurrentUser**: `object`

#### result.data.updateCurrentUser.addressLine1

> **addressLine1**: `string` = `''`

#### result.data.updateCurrentUser.addressLine2

> **addressLine2**: `string` = `''`

#### result.data.updateCurrentUser.avatarMimeType

> **avatarMimeType**: `any` = `null`

#### result.data.updateCurrentUser.avatarURL

> **avatarURL**: `any` = `null`

#### result.data.updateCurrentUser.birthDate

> **birthDate**: `any` = `null`

#### result.data.updateCurrentUser.city

> **city**: `string` = `''`

#### result.data.updateCurrentUser.countryCode

> **countryCode**: `any` = `null`

#### result.data.updateCurrentUser.createdAt

> **createdAt**: `string` = `'2023-04-13T04:53:17.742Z'`

#### result.data.updateCurrentUser.description

> **description**: `string` = `''`

#### result.data.updateCurrentUser.educationGrade

> **educationGrade**: `any` = `null`

#### result.data.updateCurrentUser.emailAddress

> **emailAddress**: `string` = `'<EMAIL>'`

#### result.data.updateCurrentUser.employmentStatus

> **employmentStatus**: `any` = `null`

#### result.data.updateCurrentUser.homePhoneNumber

> **homePhoneNumber**: `string` = `''`

#### result.data.updateCurrentUser.id

> **id**: `string` = `'65378abd-8500-8f17-1cf2-990d00000002'`

#### result.data.updateCurrentUser.isEmailAddressVerified

> **isEmailAddressVerified**: `boolean` = `true`

#### result.data.updateCurrentUser.maritalStatus

> **maritalStatus**: `any` = `null`

#### result.data.updateCurrentUser.mobilePhoneNumber

> **mobilePhoneNumber**: `string` = `''`

#### result.data.updateCurrentUser.name

> **name**: `string` = `'Bandhan Majumder'`

#### result.data.updateCurrentUser.natalSex

> **natalSex**: `any` = `null`

#### result.data.updateCurrentUser.naturalLanguageCode

> **naturalLanguageCode**: `any` = `null`

#### result.data.updateCurrentUser.postalCode

> **postalCode**: `string` = `''`

#### result.data.updateCurrentUser.role

> **role**: `string` = `'administrator'`

#### result.data.updateCurrentUser.state

> **state**: `string` = `''`

#### result.data.updateCurrentUser.updatedAt

> **updatedAt**: `string` = `'2025-02-09T06:26:51.209Z'`

#### result.data.updateCurrentUser.workPhoneNumber

> **workPhoneNumber**: `string` = `''`
