[Admin Docs](/)

***

# Interface: InterfaceRemoveUserTagModalProps

Defined in: [screens/ManageTag/removeModal/RemoveUserTagModal.tsx:38](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/ManageTag/removeModal/RemoveUserTagModal.tsx#L38)

## Properties

### handleRemoveUserTag()

> **handleRemoveUserTag**: () => `Promise`\<`void`\>

Defined in: [screens/ManageTag/removeModal/RemoveUserTagModal.tsx:41](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/ManageTag/removeModal/RemoveUserTagModal.tsx#L41)

#### Returns

`Promise`\<`void`\>

***

### removeUserTagModalIsOpen

> **removeUserTagModalIsOpen**: `boolean`

Defined in: [screens/ManageTag/removeModal/RemoveUserTagModal.tsx:39](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/ManageTag/removeModal/RemoveUserTagModal.tsx#L39)

***

### t

> **t**: `TFunction`\<`"translation"`, `"manageTag"`\>

Defined in: [screens/ManageTag/removeModal/RemoveUserTagModal.tsx:42](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/ManageTag/removeModal/RemoveUserTagModal.tsx#L42)

***

### tCommon

> **tCommon**: `TFunction`\<`"common"`, `undefined`\>

Defined in: [screens/ManageTag/removeModal/RemoveUserTagModal.tsx:43](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/ManageTag/removeModal/RemoveUserTagModal.tsx#L43)

***

### toggleRemoveUserTagModal()

> **toggleRemoveUserTagModal**: () => `void`

Defined in: [screens/ManageTag/removeModal/RemoveUserTagModal.tsx:40](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/ManageTag/removeModal/RemoveUserTagModal.tsx#L40)

#### Returns

`void`
