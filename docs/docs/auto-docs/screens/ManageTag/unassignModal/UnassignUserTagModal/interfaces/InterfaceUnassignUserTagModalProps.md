[Admin Docs](/)

***

# Interface: InterfaceUnassignUserTagModalProps

Defined in: [screens/ManageTag/unassignModal/UnassignUserTagModal.tsx:36](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/ManageTag/unassignModal/UnassignUserTagModal.tsx#L36)

## Properties

### handleUnassignUserTag()

> **handleUnassignUserTag**: () => `Promise`\<`void`\>

Defined in: [screens/ManageTag/unassignModal/UnassignUserTagModal.tsx:39](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/ManageTag/unassignModal/UnassignUserTagModal.tsx#L39)

#### Returns

`Promise`\<`void`\>

***

### t

> **t**: `TFunction`\<`"translation"`, `"manageTag"` \| `"memberDetail"`\>

Defined in: [screens/ManageTag/unassignModal/UnassignUserTagModal.tsx:40](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/ManageTag/unassignModal/UnassignUserTagModal.tsx#L40)

***

### tCommon

> **tCommon**: `TFunction`\<`"common"`, `undefined`\>

Defined in: [screens/ManageTag/unassignModal/UnassignUserTagModal.tsx:41](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/ManageTag/unassignModal/UnassignUserTagModal.tsx#L41)

***

### toggleUnassignUserTagModal()

> **toggleUnassignUserTagModal**: () => `void`

Defined in: [screens/ManageTag/unassignModal/UnassignUserTagModal.tsx:38](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/ManageTag/unassignModal/UnassignUserTagModal.tsx#L38)

#### Returns

`void`

***

### unassignUserTagModalIsOpen

> **unassignUserTagModalIsOpen**: `boolean`

Defined in: [screens/ManageTag/unassignModal/UnassignUserTagModal.tsx:37](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/ManageTag/unassignModal/UnassignUserTagModal.tsx#L37)
