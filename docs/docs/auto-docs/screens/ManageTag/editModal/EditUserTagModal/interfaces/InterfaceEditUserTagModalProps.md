[Admin Docs](/)

***

# Interface: InterfaceEditUserTagModalProps

Defined in: [screens/ManageTag/editModal/EditUserTagModal.tsx:39](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/ManageTag/editModal/EditUserTagModal.tsx#L39)

## Properties

### editUserTagModalIsOpen

> **editUserTagModalIsOpen**: `boolean`

Defined in: [screens/ManageTag/editModal/EditUserTagModal.tsx:40](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/ManageTag/editModal/EditUserTagModal.tsx#L40)

***

### handleEditUserTag()

> **handleEditUserTag**: (`e`) => `Promise`\<`void`\>

Defined in: [screens/ManageTag/editModal/EditUserTagModal.tsx:44](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/ManageTag/editModal/EditUserTagModal.tsx#L44)

#### Parameters

##### e

`FormEvent`\<`HTMLFormElement`\>

#### Returns

`Promise`\<`void`\>

***

### hideEditUserTagModal()

> **hideEditUserTagModal**: () => `void`

Defined in: [screens/ManageTag/editModal/EditUserTagModal.tsx:41](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/ManageTag/editModal/EditUserTagModal.tsx#L41)

#### Returns

`void`

***

### newTagName

> **newTagName**: `string`

Defined in: [screens/ManageTag/editModal/EditUserTagModal.tsx:42](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/ManageTag/editModal/EditUserTagModal.tsx#L42)

***

### setNewTagName()

> **setNewTagName**: (`state`) => `void`

Defined in: [screens/ManageTag/editModal/EditUserTagModal.tsx:43](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/ManageTag/editModal/EditUserTagModal.tsx#L43)

#### Parameters

##### state

`SetStateAction`\<`string`\>

#### Returns

`void`

***

### t

> **t**: `TFunction`\<`"translation"`, `"manageTag"`\>

Defined in: [screens/ManageTag/editModal/EditUserTagModal.tsx:45](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/ManageTag/editModal/EditUserTagModal.tsx#L45)

***

### tCommon

> **tCommon**: `TFunction`\<`"common"`, `undefined`\>

Defined in: [screens/ManageTag/editModal/EditUserTagModal.tsx:46](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/ManageTag/editModal/EditUserTagModal.tsx#L46)
