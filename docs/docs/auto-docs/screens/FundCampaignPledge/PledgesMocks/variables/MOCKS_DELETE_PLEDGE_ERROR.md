[Admin Docs](/)

***

# Variable: MOCKS\_DELETE\_PLEDGE\_ERROR

> `const` **MOCKS\_DELETE\_PLEDGE\_ERROR**: (\{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `id`: `string`; \}; \}; `result`: \{ `data`: \{ `organizations`: `object`[]; \}; \}; \} \| \{ `error`: `Error`; `request`: \{ `query`: `DocumentNode`; `variables`: \{ `id`: `string`; \}; \}; \})[]

Defined in: [screens/FundCampaignPledge/PledgesMocks.ts:371](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/FundCampaignPledge/PledgesMocks.ts#L371)
