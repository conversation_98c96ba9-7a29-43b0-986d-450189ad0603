[Admin Docs](/)

***

# Variable: MOCKS

> `const` **MOCKS**: (\{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `id`: `string`; \}; \}; `result`: \{ `data`: \{ `organizations`: `object`[]; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `id`: `undefined`; `pledgeOrderBy`: `string`; `where`: \{ `id`: `string`; \}; \}; \}; `result`: \{ `data`: \{ `getFundraisingCampaigns`: `object`[]; `removeFundraisingCampaignPledge`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `id`: `string`; `pledgeOrderBy`: `undefined`; `where`: `undefined`; \}; \}; `result`: \{ `data`: \{ `getFundraisingCampaigns`: `undefined`; `removeFundraisingCampaignPledge`: \{ `id`: `string`; \}; \}; \}; \})[]

Defined in: [screens/FundCampaignPledge/PledgesMocks.ts:46](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/FundCampaignPledge/PledgesMocks.ts#L46)
