[Admin Docs](/)

***

# Variable: EMPTY\_MOCKS

> `const` **EMPTY\_MOCKS**: (\{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `id`: `string`; \}; \}; `result`: \{ `data`: \{ `organizations`: `object`[]; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `pledgeOrderBy`: `string`; `where`: \{ `id`: `string`; \}; \}; \}; `result`: \{ `data`: \{ `getFundraisingCampaigns`: `object`[]; \}; \}; \})[]

Defined in: [screens/FundCampaignPledge/PledgesMocks.ts:384](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/FundCampaignPledge/PledgesMocks.ts#L384)
