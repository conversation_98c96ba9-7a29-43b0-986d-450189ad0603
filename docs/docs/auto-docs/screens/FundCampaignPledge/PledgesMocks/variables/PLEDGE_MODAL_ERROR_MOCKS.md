[Admin Docs](/)

***

# Variable: PLEDGE\_MODAL\_ERROR\_MOCKS

> `const` **PLEDGE\_MODAL\_ERROR\_MOCKS**: (\{ `error`: `Error`; `request`: \{ `query`: `DocumentNode`; `variables`: \{ `amount`: `number`; `campaignId`: `string`; `currency`: `string`; `endDate`: `any`; `id`: `undefined`; `startDate`: `any`; `userIds`: `string`[]; `users`: `undefined`; \}; \}; `result`: `undefined`; \} \| \{ `error`: `Error`; `request`: \{ `query`: `DocumentNode`; `variables`: \{ `amount`: `number`; `campaignId`: `undefined`; `currency`: `string`; `endDate`: `any`; `id`: `string`; `startDate`: `any`; `userIds`: `undefined`; `users`: `string`[]; \}; \}; `result`: `undefined`; \} \| \{ `error`: `undefined`; `request`: \{ `query`: `DocumentNode`; `variables`: \{ `amount`: `undefined`; `campaignId`: `undefined`; `currency`: `undefined`; `endDate`: `undefined`; `id`: `string`; `startDate`: `undefined`; `userIds`: `undefined`; `users`: `undefined`; \}; \}; `result`: \{ `data`: \{ `organizations`: `object`[]; \}; \}; \})[]

Defined in: [screens/FundCampaignPledge/PledgesMocks.ts:536](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/FundCampaignPledge/PledgesMocks.ts#L536)
