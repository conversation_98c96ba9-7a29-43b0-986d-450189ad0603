[Admin Docs](/)

***

# Variable: PLEDGE\_MODAL\_MOCKS

> `const` **PLEDGE\_MODAL\_MOCKS**: (\{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `id`: `string`; \}; \}; `result`: \{ `data`: \{ `organizations`: `object`[]; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `amount`: `number`; `campaignId`: `undefined`; `currency`: `undefined`; `endDate`: `undefined`; `id`: `string`; `startDate`: `undefined`; `userIds`: `undefined`; \}; \}; `result`: \{ `data`: \{ `createFundraisingCampaignPledge`: `undefined`; `createPledge`: `undefined`; `updateFundraisingCampaignPledge`: \{ `id`: `string`; \}; `updatePledge`: `undefined`; `user`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `amount`: `number`; `campaignId`: `string`; `currency`: `string`; `endDate`: `string`; `id`: `undefined`; `startDate`: `string`; `userIds`: `string`[]; \}; \}; `result`: \{ `data`: \{ `createFundraisingCampaignPledge`: \{ `id`: `string`; \}; `createPledge`: `undefined`; `updateFundraisingCampaignPledge`: `undefined`; `updatePledge`: `undefined`; `user`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `amount`: `number`; `campaignId`: `string`; `currency`: `string`; `endDate`: `string`; `id`: `undefined`; `startDate`: `string`; `userIds`: `string`[]; \}; \}; `result`: \{ `data`: \{ `createFundraisingCampaignPledge`: `undefined`; `createPledge`: \{ `amount`: `number`; `currency`: `string`; `endDate`: `string`; `id`: `string`; `startDate`: `string`; `users`: `object`[]; \}; `updateFundraisingCampaignPledge`: `undefined`; `updatePledge`: `undefined`; `user`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `amount`: `number`; `campaignId`: `undefined`; `currency`: `undefined`; `endDate`: `undefined`; `id`: `string`; `startDate`: `undefined`; `userIds`: `undefined`; \}; \}; `result`: \{ `data`: \{ `createFundraisingCampaignPledge`: `undefined`; `createPledge`: `undefined`; `updateFundraisingCampaignPledge`: `undefined`; `updatePledge`: \{ `amount`: `number`; `currency`: `string`; `endDate`: `string`; `id`: `string`; `startDate`: `string`; `users`: `object`[]; \}; `user`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `amount`: `undefined`; `campaignId`: `undefined`; `currency`: `undefined`; `endDate`: `undefined`; `id`: `string`; `startDate`: `undefined`; `userIds`: `undefined`; \}; \}; `result`: \{ `data`: \{ `createFundraisingCampaignPledge`: `undefined`; `createPledge`: `undefined`; `updateFundraisingCampaignPledge`: `undefined`; `updatePledge`: `undefined`; `user`: \{ `firstName`: `string`; `id`: `string`; `image`: `any`; `lastName`: `string`; \}; \}; \}; \})[]

Defined in: [screens/FundCampaignPledge/PledgesMocks.ts:416](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/FundCampaignPledge/PledgesMocks.ts#L416)
