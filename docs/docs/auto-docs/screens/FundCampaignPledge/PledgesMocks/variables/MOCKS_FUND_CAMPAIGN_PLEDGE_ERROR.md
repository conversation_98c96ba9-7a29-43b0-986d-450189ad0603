[Admin Docs](/)

***

# Variable: MOCKS\_FUND\_CAMPAIGN\_PLEDGE\_ERROR

> `const` **MOCKS\_FUND\_CAMPAIGN\_PLEDGE\_ERROR**: (\{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `id`: `string`; \}; \}; `result`: \{ `data`: \{ `organizations`: `object`[]; \}; \}; \} \| \{ `error`: `Error`; `request`: \{ `query`: `DocumentNode`; `variables`: \{ `pledgeOrderBy`: `string`; `where`: \{ `id`: `string`; \}; \}; \}; \})[]

Defined in: [screens/FundCampaignPledge/PledgesMocks.ts:355](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/FundCampaignPledge/PledgesMocks.ts#L355)
