[Admin Docs](/)

***

# Interface: InterfaceVolunteerCreateModal

Defined in: [screens/EventVolunteers/Volunteers/createModal/VolunteerCreateModal.tsx:46](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/EventVolunteers/Volunteers/createModal/VolunteerCreateModal.tsx#L46)

## Properties

### eventId

> **eventId**: `string`

Defined in: [screens/EventVolunteers/Volunteers/createModal/VolunteerCreateModal.tsx:49](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/EventVolunteers/Volunteers/createModal/VolunteerCreateModal.tsx#L49)

***

### hide()

> **hide**: () => `void`

Defined in: [screens/EventVolunteers/Volunteers/createModal/VolunteerCreateModal.tsx:48](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/EventVolunteers/Volunteers/createModal/VolunteerCreateModal.tsx#L48)

#### Returns

`void`

***

### isOpen

> **isOpen**: `boolean`

Defined in: [screens/EventVolunteers/Volunteers/createModal/VolunteerCreateModal.tsx:47](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/EventVolunteers/Volunteers/createModal/VolunteerCreateModal.tsx#L47)

***

### orgId

> **orgId**: `string`

Defined in: [screens/EventVolunteers/Volunteers/createModal/VolunteerCreateModal.tsx:50](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/EventVolunteers/Volunteers/createModal/VolunteerCreateModal.tsx#L50)

***

### refetchVolunteers()

> **refetchVolunteers**: () => `void`

Defined in: [screens/EventVolunteers/Volunteers/createModal/VolunteerCreateModal.tsx:51](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/EventVolunteers/Volunteers/createModal/VolunteerCreateModal.tsx#L51)

#### Returns

`void`
