[Admin Docs](/)

***

# Interface: InterfaceVolunteerViewModal

Defined in: [screens/EventVolunteers/Volunteers/viewModal/VolunteerViewModal.tsx:64](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/EventVolunteers/Volunteers/viewModal/VolunteerViewModal.tsx#L64)

## Properties

### hide()

> **hide**: () => `void`

Defined in: [screens/EventVolunteers/Volunteers/viewModal/VolunteerViewModal.tsx:66](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/EventVolunteers/Volunteers/viewModal/VolunteerViewModal.tsx#L66)

#### Returns

`void`

***

### isOpen

> **isOpen**: `boolean`

Defined in: [screens/EventVolunteers/Volunteers/viewModal/VolunteerViewModal.tsx:65](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/EventVolunteers/Volunteers/viewModal/VolunteerViewModal.tsx#L65)

***

### volunteer

> **volunteer**: [`InterfaceEventVolunteerInfo`](../../../../../../utils/interfaces/interfaces/InterfaceEventVolunteerInfo.md)

Defined in: [screens/EventVolunteers/Volunteers/viewModal/VolunteerViewModal.tsx:67](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/EventVolunteers/Volunteers/viewModal/VolunteerViewModal.tsx#L67)
