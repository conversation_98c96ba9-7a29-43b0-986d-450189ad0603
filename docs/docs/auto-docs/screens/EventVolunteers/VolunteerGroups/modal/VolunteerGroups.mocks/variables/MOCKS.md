[Admin Docs](/)

***

# Variable: MOCKS

> `const` **MOCKS**: (\{ `request`: \{ `query`: `any`; `variables`: \{ `data`: `undefined`; `id`: `undefined`; `orderBy`: `string`; `where`: \{ `eventId`: `string`; `leaderName`: `any`; `name_contains`: `string`; \}; \}; \}; `result`: \{ `data`: \{ `createEventVolunteerGroup`: `undefined`; `getEventVolunteerGroups`: `object`[]; `organizations`: `undefined`; `removeEventVolunteerGroup`: `undefined`; `updateEventVolunteerGroup`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `any`; `variables`: \{ `data`: `undefined`; `id`: `undefined`; `orderBy`: `any`; `where`: \{ `eventId`: `string`; `leaderName`: `string`; `name_contains`: `any`; \}; \}; \}; `result`: \{ `data`: \{ `createEventVolunteerGroup`: `undefined`; `getEventVolunteerGroups`: `object`[]; `organizations`: `undefined`; `removeEventVolunteerGroup`: `undefined`; `updateEventVolunteerGroup`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `data`: `undefined`; `id`: `string`; `orderBy`: `undefined`; `where`: `undefined`; \}; \}; `result`: \{ `data`: \{ `createEventVolunteerGroup`: `undefined`; `getEventVolunteerGroups`: `undefined`; `organizations`: `object`[]; `removeEventVolunteerGroup`: `undefined`; `updateEventVolunteerGroup`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `any`; `variables`: \{ `data`: \{ `description`: `string`; `eventId`: `string`; `leaderId`: `string`; `name`: `string`; `volunteersRequired`: `number`; `volunteerUserIds`: `string`[]; \}; `id`: `undefined`; `orderBy`: `undefined`; `where`: `undefined`; \}; \}; `result`: \{ `data`: \{ `createEventVolunteerGroup`: \{ `_id`: `string`; \}; `getEventVolunteerGroups`: `undefined`; `organizations`: `undefined`; `removeEventVolunteerGroup`: `undefined`; `updateEventVolunteerGroup`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `any`; `variables`: \{ `data`: `undefined`; `id`: `string`; `orderBy`: `undefined`; `where`: `undefined`; \}; \}; `result`: \{ `data`: \{ `createEventVolunteerGroup`: `undefined`; `getEventVolunteerGroups`: `undefined`; `organizations`: `undefined`; `removeEventVolunteerGroup`: \{ `_id`: `string`; \}; `updateEventVolunteerGroup`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `any`; `variables`: \{ `data`: \{ `description`: `string`; `eventId`: `string`; `leaderId`: `undefined`; `name`: `string`; `volunteersRequired`: `number`; `volunteerUserIds`: `undefined`; \}; `id`: `string`; `orderBy`: `undefined`; `where`: `undefined`; \}; \}; `result`: \{ `data`: \{ `createEventVolunteerGroup`: `undefined`; `getEventVolunteerGroups`: `undefined`; `organizations`: `undefined`; `removeEventVolunteerGroup`: `undefined`; `updateEventVolunteerGroup`: \{ `_id`: `string`; \}; \}; \}; \} \| \{ `request`: \{ `query`: `any`; `variables`: \{ `data`: \{ `description`: `undefined`; `eventId`: `string`; `leaderId`: `undefined`; `name`: `undefined`; `volunteersRequired`: `undefined`; `volunteerUserIds`: `undefined`; \}; `id`: `string`; `orderBy`: `undefined`; `where`: `undefined`; \}; \}; `result`: \{ `data`: \{ `createEventVolunteerGroup`: `undefined`; `getEventVolunteerGroups`: `undefined`; `organizations`: `undefined`; `removeEventVolunteerGroup`: `undefined`; `updateEventVolunteerGroup`: \{ `_id`: `string`; \}; \}; \}; \})[]

Defined in: [screens/EventVolunteers/VolunteerGroups/modal/VolunteerGroups.mocks.ts:114](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/EventVolunteers/VolunteerGroups/modal/VolunteerGroups.mocks.ts#L114)
