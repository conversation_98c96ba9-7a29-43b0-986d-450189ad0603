[Admin <PERSON>s](/)

***

# Variable: MOCKS\_ERROR

> `const` **MOCKS\_ERROR**: (\{ `error`: `Error`; `request`: \{ `query`: `any`; `variables`: \{ `data`: `undefined`; `id`: `undefined`; `orderBy`: `any`; `where`: \{ `eventId`: `string`; `leaderName`: `any`; `name_contains`: `string`; \}; \}; \}; `result`: `undefined`; \} \| \{ `error`: `Error`; `request`: \{ `query`: `any`; `variables`: \{ `data`: `undefined`; `id`: `string`; `orderBy`: `undefined`; `where`: `undefined`; \}; \}; `result`: `undefined`; \} \| \{ `error`: `undefined`; `request`: \{ `query`: `DocumentNode`; `variables`: \{ `data`: `undefined`; `id`: `string`; `orderBy`: `undefined`; `where`: `undefined`; \}; \}; `result`: \{ `data`: \{ `organizations`: `object`[]; \}; \}; \} \| \{ `error`: `Error`; `request`: \{ `query`: `any`; `variables`: \{ `data`: \{ `description`: `string`; `eventId`: `string`; `leaderId`: `string`; `name`: `string`; `volunteersRequired`: `number`; `volunteerUserIds`: `string`[]; \}; `id`: `undefined`; `orderBy`: `undefined`; `where`: `undefined`; \}; \}; `result`: `undefined`; \} \| \{ `error`: `Error`; `request`: \{ `query`: `any`; `variables`: \{ `data`: \{ `description`: `string`; `eventId`: `string`; `leaderId`: `undefined`; `name`: `string`; `volunteersRequired`: `number`; `volunteerUserIds`: `undefined`; \}; `id`: `string`; `orderBy`: `undefined`; `where`: `undefined`; \}; \}; `result`: `undefined`; \})[]

Defined in: [screens/EventVolunteers/VolunteerGroups/modal/VolunteerGroups.mocks.ts:360](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/screens/EventVolunteers/VolunteerGroups/modal/VolunteerGroups.mocks.ts#L360)
