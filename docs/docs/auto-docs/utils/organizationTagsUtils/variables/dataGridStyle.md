[Admin Docs](/)

***

# Variable: dataGridStyle

> `const` **dataGridStyle**: `object`

Defined in: [utils/organizationTagsUtils.ts:12](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/organizationTagsUtils.ts#L12)

## Type declaration

#### & .MuiDataGrid-cell:focus

> **MuiDataGrid-cell:focus**: `object`

#### & .MuiDataGrid-cell:focus.outline

> **outline**: `string` = `'2px solid #000'`

#### & .MuiDataGrid-cell:focus.outlineOffset

> **outlineOffset**: `string` = `'-2px'`

#### & .MuiDataGrid-main

> **MuiDataGrid-main**: `object`

#### & .MuiDataGrid-main.borderRadius

> **borderRadius**: `string` = `'0.1rem'`

#### & .MuiDataGrid-root

> **MuiDataGrid-root**: `object`

#### & .MuiDataGrid-root.borderRadius

> **borderRadius**: `string` = `'0.1rem'`

#### & .MuiDataGrid-row:hover

> **MuiDataGrid-row:hover**: `object`

#### & .MuiDataGrid-row:hover.backgroundColor

> **backgroundColor**: `string` = `'transparent'`

#### & .MuiDataGrid-row:hover.boxShadow

> **boxShadow**: `string` = `'0 0 0 1px rgba(0, 0, 0, 0.1)'`

#### & .MuiDataGrid-row.Mui-hovered

> **Mui-hovered**: `object`

#### & .MuiDataGrid-row.Mui-hovered.backgroundColor

> **backgroundColor**: `string` = `'transparent'`

#### & .MuiDataGrid-row.Mui-hovered.boxShadow

> **boxShadow**: `string` = `'0 0 0 1px rgba(0, 0, 0, 0.1)'`

#### & .MuiDataGrid-topContainer

> **MuiDataGrid-topContainer**: `object`

#### & .MuiDataGrid-topContainer.position

> **position**: `string` = `'fixed'`

#### & .MuiDataGrid-topContainer.top

> **top**: `number` = `290`

#### & .MuiDataGrid-topContainer.zIndex

> **zIndex**: `number` = `1`

#### & .MuiDataGrid-virtualScrollerContent

> **MuiDataGrid-virtualScrollerContent**: `object`

#### & .MuiDataGrid-virtualScrollerContent.marginTop

> **marginTop**: `number` = `6.5`

#### &.MuiDataGrid-root .MuiDataGrid-cell:focus-within

> **MuiDataGrid-cell:focus-within**: `object`

#### &.MuiDataGrid-root .MuiDataGrid-cell:focus-within.outline

> **outline**: `string` = `'none !important'`

#### &.MuiDataGrid-root .MuiDataGrid-columnHeader:focus-within

> **MuiDataGrid-columnHeader:focus-within**: `object`

#### &.MuiDataGrid-root .MuiDataGrid-columnHeader:focus-within.outline

> **outline**: `string` = `'none'`
