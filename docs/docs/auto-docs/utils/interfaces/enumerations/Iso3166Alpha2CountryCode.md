[Admin Docs](/)

***

# Enumeration: Iso3166Alpha2CountryCode

Defined in: [utils/interfaces.ts:1](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1)

## Enumeration Members

### ad

> **ad**: `"ad"`

Defined in: [utils/interfaces.ts:2](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L2)

***

### ae

> **ae**: `"ae"`

Defined in: [utils/interfaces.ts:3](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L3)

***

### af

> **af**: `"af"`

Defined in: [utils/interfaces.ts:4](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L4)

***

### ag

> **ag**: `"ag"`

Defined in: [utils/interfaces.ts:5](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L5)

***

### ai

> **ai**: `"ai"`

Defined in: [utils/interfaces.ts:6](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L6)

***

### al

> **al**: `"al"`

Defined in: [utils/interfaces.ts:7](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L7)

***

### am

> **am**: `"am"`

Defined in: [utils/interfaces.ts:8](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L8)

***

### ao

> **ao**: `"ao"`

Defined in: [utils/interfaces.ts:9](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L9)

***

### aq

> **aq**: `"aq"`

Defined in: [utils/interfaces.ts:10](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L10)

***

### ar

> **ar**: `"ar"`

Defined in: [utils/interfaces.ts:11](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L11)

***

### as

> **as**: `"as"`

Defined in: [utils/interfaces.ts:12](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L12)

***

### at

> **at**: `"at"`

Defined in: [utils/interfaces.ts:13](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L13)

***

### au

> **au**: `"au"`

Defined in: [utils/interfaces.ts:14](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L14)

***

### aw

> **aw**: `"aw"`

Defined in: [utils/interfaces.ts:15](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L15)

***

### ax

> **ax**: `"ax"`

Defined in: [utils/interfaces.ts:16](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L16)

***

### az

> **az**: `"az"`

Defined in: [utils/interfaces.ts:17](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L17)

***

### ba

> **ba**: `"ba"`

Defined in: [utils/interfaces.ts:18](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L18)

***

### bb

> **bb**: `"bb"`

Defined in: [utils/interfaces.ts:19](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L19)

***

### bd

> **bd**: `"bd"`

Defined in: [utils/interfaces.ts:20](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L20)

***

### be

> **be**: `"be"`

Defined in: [utils/interfaces.ts:21](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L21)

***

### bf

> **bf**: `"bf"`

Defined in: [utils/interfaces.ts:22](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L22)

***

### bg

> **bg**: `"bg"`

Defined in: [utils/interfaces.ts:23](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L23)

***

### bh

> **bh**: `"bh"`

Defined in: [utils/interfaces.ts:24](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L24)

***

### bi

> **bi**: `"bi"`

Defined in: [utils/interfaces.ts:25](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L25)

***

### bj

> **bj**: `"bj"`

Defined in: [utils/interfaces.ts:26](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L26)

***

### bl

> **bl**: `"bl"`

Defined in: [utils/interfaces.ts:27](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L27)

***

### bm

> **bm**: `"bm"`

Defined in: [utils/interfaces.ts:28](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L28)

***

### bn

> **bn**: `"bn"`

Defined in: [utils/interfaces.ts:29](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L29)

***

### bo

> **bo**: `"bo"`

Defined in: [utils/interfaces.ts:30](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L30)

***

### bq

> **bq**: `"bq"`

Defined in: [utils/interfaces.ts:31](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L31)

***

### br

> **br**: `"br"`

Defined in: [utils/interfaces.ts:32](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L32)

***

### bs

> **bs**: `"bs"`

Defined in: [utils/interfaces.ts:33](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L33)

***

### bt

> **bt**: `"bt"`

Defined in: [utils/interfaces.ts:34](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L34)

***

### bv

> **bv**: `"bv"`

Defined in: [utils/interfaces.ts:35](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L35)

***

### bw

> **bw**: `"bw"`

Defined in: [utils/interfaces.ts:36](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L36)

***

### by

> **by**: `"by"`

Defined in: [utils/interfaces.ts:37](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L37)

***

### bz

> **bz**: `"bz"`

Defined in: [utils/interfaces.ts:38](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L38)

***

### ca

> **ca**: `"ca"`

Defined in: [utils/interfaces.ts:39](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L39)

***

### cc

> **cc**: `"cc"`

Defined in: [utils/interfaces.ts:40](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L40)

***

### cd

> **cd**: `"cd"`

Defined in: [utils/interfaces.ts:41](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L41)

***

### cf

> **cf**: `"cf"`

Defined in: [utils/interfaces.ts:42](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L42)

***

### cg

> **cg**: `"cg"`

Defined in: [utils/interfaces.ts:43](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L43)

***

### ch

> **ch**: `"ch"`

Defined in: [utils/interfaces.ts:44](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L44)

***

### ci

> **ci**: `"ci"`

Defined in: [utils/interfaces.ts:45](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L45)

***

### ck

> **ck**: `"ck"`

Defined in: [utils/interfaces.ts:46](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L46)

***

### cl

> **cl**: `"cl"`

Defined in: [utils/interfaces.ts:47](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L47)

***

### cm

> **cm**: `"cm"`

Defined in: [utils/interfaces.ts:48](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L48)

***

### cn

> **cn**: `"cn"`

Defined in: [utils/interfaces.ts:49](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L49)

***

### co

> **co**: `"co"`

Defined in: [utils/interfaces.ts:50](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L50)

***

### cr

> **cr**: `"cr"`

Defined in: [utils/interfaces.ts:51](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L51)

***

### cu

> **cu**: `"cu"`

Defined in: [utils/interfaces.ts:52](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L52)

***

### cv

> **cv**: `"cv"`

Defined in: [utils/interfaces.ts:53](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L53)

***

### cw

> **cw**: `"cw"`

Defined in: [utils/interfaces.ts:54](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L54)

***

### cx

> **cx**: `"cx"`

Defined in: [utils/interfaces.ts:55](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L55)

***

### cy

> **cy**: `"cy"`

Defined in: [utils/interfaces.ts:56](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L56)

***

### cz

> **cz**: `"cz"`

Defined in: [utils/interfaces.ts:57](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L57)

***

### de

> **de**: `"de"`

Defined in: [utils/interfaces.ts:58](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L58)

***

### dj

> **dj**: `"dj"`

Defined in: [utils/interfaces.ts:59](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L59)

***

### dk

> **dk**: `"dk"`

Defined in: [utils/interfaces.ts:60](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L60)

***

### dm

> **dm**: `"dm"`

Defined in: [utils/interfaces.ts:61](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L61)

***

### do

> **do**: `"do"`

Defined in: [utils/interfaces.ts:62](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L62)

***

### dz

> **dz**: `"dz"`

Defined in: [utils/interfaces.ts:63](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L63)

***

### ec

> **ec**: `"ec"`

Defined in: [utils/interfaces.ts:64](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L64)

***

### ee

> **ee**: `"ee"`

Defined in: [utils/interfaces.ts:65](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L65)

***

### eg

> **eg**: `"eg"`

Defined in: [utils/interfaces.ts:66](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L66)

***

### eh

> **eh**: `"eh"`

Defined in: [utils/interfaces.ts:67](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L67)

***

### er

> **er**: `"er"`

Defined in: [utils/interfaces.ts:68](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L68)

***

### es

> **es**: `"es"`

Defined in: [utils/interfaces.ts:69](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L69)

***

### et

> **et**: `"et"`

Defined in: [utils/interfaces.ts:70](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L70)

***

### fi

> **fi**: `"fi"`

Defined in: [utils/interfaces.ts:71](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L71)

***

### fj

> **fj**: `"fj"`

Defined in: [utils/interfaces.ts:72](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L72)

***

### fk

> **fk**: `"fk"`

Defined in: [utils/interfaces.ts:73](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L73)

***

### fm

> **fm**: `"fm"`

Defined in: [utils/interfaces.ts:74](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L74)

***

### fo

> **fo**: `"fo"`

Defined in: [utils/interfaces.ts:75](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L75)

***

### fr

> **fr**: `"fr"`

Defined in: [utils/interfaces.ts:76](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L76)

***

### ga

> **ga**: `"ga"`

Defined in: [utils/interfaces.ts:77](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L77)

***

### gb

> **gb**: `"gb"`

Defined in: [utils/interfaces.ts:78](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L78)

***

### gd

> **gd**: `"gd"`

Defined in: [utils/interfaces.ts:79](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L79)

***

### ge

> **ge**: `"ge"`

Defined in: [utils/interfaces.ts:80](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L80)

***

### gf

> **gf**: `"gf"`

Defined in: [utils/interfaces.ts:81](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L81)

***

### gg

> **gg**: `"gg"`

Defined in: [utils/interfaces.ts:82](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L82)

***

### gh

> **gh**: `"gh"`

Defined in: [utils/interfaces.ts:83](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L83)

***

### gi

> **gi**: `"gi"`

Defined in: [utils/interfaces.ts:84](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L84)

***

### gl

> **gl**: `"gl"`

Defined in: [utils/interfaces.ts:85](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L85)

***

### gm

> **gm**: `"gm"`

Defined in: [utils/interfaces.ts:86](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L86)

***

### gn

> **gn**: `"gn"`

Defined in: [utils/interfaces.ts:87](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L87)

***

### gp

> **gp**: `"gp"`

Defined in: [utils/interfaces.ts:88](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L88)

***

### gq

> **gq**: `"gq"`

Defined in: [utils/interfaces.ts:89](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L89)

***

### gr

> **gr**: `"gr"`

Defined in: [utils/interfaces.ts:90](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L90)

***

### gs

> **gs**: `"gs"`

Defined in: [utils/interfaces.ts:91](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L91)

***

### gt

> **gt**: `"gt"`

Defined in: [utils/interfaces.ts:92](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L92)

***

### gu

> **gu**: `"gu"`

Defined in: [utils/interfaces.ts:93](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L93)

***

### gw

> **gw**: `"gw"`

Defined in: [utils/interfaces.ts:94](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L94)

***

### gy

> **gy**: `"gy"`

Defined in: [utils/interfaces.ts:95](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L95)

***

### hk

> **hk**: `"hk"`

Defined in: [utils/interfaces.ts:96](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L96)

***

### hm

> **hm**: `"hm"`

Defined in: [utils/interfaces.ts:97](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L97)

***

### hn

> **hn**: `"hn"`

Defined in: [utils/interfaces.ts:98](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L98)

***

### hr

> **hr**: `"hr"`

Defined in: [utils/interfaces.ts:99](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L99)

***

### ht

> **ht**: `"ht"`

Defined in: [utils/interfaces.ts:100](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L100)

***

### hu

> **hu**: `"hu"`

Defined in: [utils/interfaces.ts:101](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L101)

***

### id

> **id**: `"id"`

Defined in: [utils/interfaces.ts:102](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L102)

***

### ie

> **ie**: `"ie"`

Defined in: [utils/interfaces.ts:103](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L103)

***

### il

> **il**: `"il"`

Defined in: [utils/interfaces.ts:104](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L104)

***

### im

> **im**: `"im"`

Defined in: [utils/interfaces.ts:105](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L105)

***

### in

> **in**: `"in"`

Defined in: [utils/interfaces.ts:106](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L106)

***

### io

> **io**: `"io"`

Defined in: [utils/interfaces.ts:107](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L107)

***

### iq

> **iq**: `"iq"`

Defined in: [utils/interfaces.ts:108](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L108)

***

### ir

> **ir**: `"ir"`

Defined in: [utils/interfaces.ts:109](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L109)

***

### is

> **is**: `"is"`

Defined in: [utils/interfaces.ts:110](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L110)

***

### it

> **it**: `"it"`

Defined in: [utils/interfaces.ts:111](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L111)

***

### je

> **je**: `"je"`

Defined in: [utils/interfaces.ts:112](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L112)

***

### jm

> **jm**: `"jm"`

Defined in: [utils/interfaces.ts:113](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L113)

***

### jo

> **jo**: `"jo"`

Defined in: [utils/interfaces.ts:114](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L114)

***

### jp

> **jp**: `"jp"`

Defined in: [utils/interfaces.ts:115](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L115)

***

### ke

> **ke**: `"ke"`

Defined in: [utils/interfaces.ts:116](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L116)

***

### kg

> **kg**: `"kg"`

Defined in: [utils/interfaces.ts:117](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L117)

***

### kh

> **kh**: `"kh"`

Defined in: [utils/interfaces.ts:118](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L118)

***

### ki

> **ki**: `"ki"`

Defined in: [utils/interfaces.ts:119](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L119)

***

### km

> **km**: `"km"`

Defined in: [utils/interfaces.ts:120](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L120)

***

### kn

> **kn**: `"kn"`

Defined in: [utils/interfaces.ts:121](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L121)

***

### kp

> **kp**: `"kp"`

Defined in: [utils/interfaces.ts:122](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L122)

***

### kr

> **kr**: `"kr"`

Defined in: [utils/interfaces.ts:123](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L123)

***

### kw

> **kw**: `"kw"`

Defined in: [utils/interfaces.ts:124](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L124)

***

### ky

> **ky**: `"ky"`

Defined in: [utils/interfaces.ts:125](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L125)

***

### kz

> **kz**: `"kz"`

Defined in: [utils/interfaces.ts:126](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L126)

***

### la

> **la**: `"la"`

Defined in: [utils/interfaces.ts:127](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L127)

***

### lb

> **lb**: `"lb"`

Defined in: [utils/interfaces.ts:128](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L128)

***

### lc

> **lc**: `"lc"`

Defined in: [utils/interfaces.ts:129](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L129)

***

### li

> **li**: `"li"`

Defined in: [utils/interfaces.ts:130](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L130)

***

### lk

> **lk**: `"lk"`

Defined in: [utils/interfaces.ts:131](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L131)

***

### lr

> **lr**: `"lr"`

Defined in: [utils/interfaces.ts:132](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L132)

***

### ls

> **ls**: `"ls"`

Defined in: [utils/interfaces.ts:133](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L133)

***

### lt

> **lt**: `"lt"`

Defined in: [utils/interfaces.ts:134](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L134)

***

### lu

> **lu**: `"lu"`

Defined in: [utils/interfaces.ts:135](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L135)

***

### lv

> **lv**: `"lv"`

Defined in: [utils/interfaces.ts:136](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L136)

***

### ly

> **ly**: `"ly"`

Defined in: [utils/interfaces.ts:137](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L137)

***

### ma

> **ma**: `"ma"`

Defined in: [utils/interfaces.ts:138](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L138)

***

### mc

> **mc**: `"mc"`

Defined in: [utils/interfaces.ts:139](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L139)

***

### md

> **md**: `"md"`

Defined in: [utils/interfaces.ts:140](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L140)

***

### me

> **me**: `"me"`

Defined in: [utils/interfaces.ts:141](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L141)

***

### mf

> **mf**: `"mf"`

Defined in: [utils/interfaces.ts:142](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L142)

***

### mg

> **mg**: `"mg"`

Defined in: [utils/interfaces.ts:143](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L143)

***

### mh

> **mh**: `"mh"`

Defined in: [utils/interfaces.ts:144](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L144)

***

### mk

> **mk**: `"mk"`

Defined in: [utils/interfaces.ts:145](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L145)

***

### ml

> **ml**: `"ml"`

Defined in: [utils/interfaces.ts:146](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L146)

***

### mm

> **mm**: `"mm"`

Defined in: [utils/interfaces.ts:147](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L147)

***

### mn

> **mn**: `"mn"`

Defined in: [utils/interfaces.ts:148](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L148)

***

### mo

> **mo**: `"mo"`

Defined in: [utils/interfaces.ts:149](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L149)

***

### mp

> **mp**: `"mp"`

Defined in: [utils/interfaces.ts:150](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L150)

***

### mq

> **mq**: `"mq"`

Defined in: [utils/interfaces.ts:151](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L151)

***

### mr

> **mr**: `"mr"`

Defined in: [utils/interfaces.ts:152](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L152)

***

### ms

> **ms**: `"ms"`

Defined in: [utils/interfaces.ts:153](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L153)

***

### mt

> **mt**: `"mt"`

Defined in: [utils/interfaces.ts:154](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L154)

***

### mu

> **mu**: `"mu"`

Defined in: [utils/interfaces.ts:155](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L155)

***

### mv

> **mv**: `"mv"`

Defined in: [utils/interfaces.ts:156](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L156)

***

### mw

> **mw**: `"mw"`

Defined in: [utils/interfaces.ts:157](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L157)

***

### mx

> **mx**: `"mx"`

Defined in: [utils/interfaces.ts:158](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L158)

***

### my

> **my**: `"my"`

Defined in: [utils/interfaces.ts:159](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L159)

***

### mz

> **mz**: `"mz"`

Defined in: [utils/interfaces.ts:160](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L160)

***

### na

> **na**: `"na"`

Defined in: [utils/interfaces.ts:161](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L161)

***

### nc

> **nc**: `"nc"`

Defined in: [utils/interfaces.ts:162](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L162)

***

### ne

> **ne**: `"ne"`

Defined in: [utils/interfaces.ts:163](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L163)

***

### nf

> **nf**: `"nf"`

Defined in: [utils/interfaces.ts:164](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L164)

***

### ng

> **ng**: `"ng"`

Defined in: [utils/interfaces.ts:165](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L165)

***

### ni

> **ni**: `"ni"`

Defined in: [utils/interfaces.ts:166](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L166)

***

### nl

> **nl**: `"nl"`

Defined in: [utils/interfaces.ts:167](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L167)

***

### no

> **no**: `"no"`

Defined in: [utils/interfaces.ts:168](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L168)

***

### np

> **np**: `"np"`

Defined in: [utils/interfaces.ts:169](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L169)

***

### nr

> **nr**: `"nr"`

Defined in: [utils/interfaces.ts:170](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L170)

***

### nu

> **nu**: `"nu"`

Defined in: [utils/interfaces.ts:171](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L171)

***

### nz

> **nz**: `"nz"`

Defined in: [utils/interfaces.ts:172](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L172)

***

### om

> **om**: `"om"`

Defined in: [utils/interfaces.ts:173](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L173)

***

### pa

> **pa**: `"pa"`

Defined in: [utils/interfaces.ts:174](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L174)

***

### pe

> **pe**: `"pe"`

Defined in: [utils/interfaces.ts:175](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L175)

***

### pf

> **pf**: `"pf"`

Defined in: [utils/interfaces.ts:176](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L176)

***

### pg

> **pg**: `"pg"`

Defined in: [utils/interfaces.ts:177](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L177)

***

### ph

> **ph**: `"ph"`

Defined in: [utils/interfaces.ts:178](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L178)

***

### pk

> **pk**: `"pk"`

Defined in: [utils/interfaces.ts:179](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L179)

***

### pl

> **pl**: `"pl"`

Defined in: [utils/interfaces.ts:180](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L180)

***

### pm

> **pm**: `"pm"`

Defined in: [utils/interfaces.ts:181](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L181)

***

### pn

> **pn**: `"pn"`

Defined in: [utils/interfaces.ts:182](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L182)

***

### pr

> **pr**: `"pr"`

Defined in: [utils/interfaces.ts:183](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L183)

***

### ps

> **ps**: `"ps"`

Defined in: [utils/interfaces.ts:184](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L184)

***

### pt

> **pt**: `"pt"`

Defined in: [utils/interfaces.ts:185](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L185)

***

### pw

> **pw**: `"pw"`

Defined in: [utils/interfaces.ts:186](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L186)

***

### py

> **py**: `"py"`

Defined in: [utils/interfaces.ts:187](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L187)

***

### qa

> **qa**: `"qa"`

Defined in: [utils/interfaces.ts:188](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L188)

***

### re

> **re**: `"re"`

Defined in: [utils/interfaces.ts:189](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L189)

***

### ro

> **ro**: `"ro"`

Defined in: [utils/interfaces.ts:190](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L190)

***

### rs

> **rs**: `"rs"`

Defined in: [utils/interfaces.ts:191](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L191)

***

### ru

> **ru**: `"ru"`

Defined in: [utils/interfaces.ts:192](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L192)

***

### rw

> **rw**: `"rw"`

Defined in: [utils/interfaces.ts:193](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L193)

***

### sa

> **sa**: `"sa"`

Defined in: [utils/interfaces.ts:194](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L194)

***

### sb

> **sb**: `"sb"`

Defined in: [utils/interfaces.ts:195](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L195)

***

### sc

> **sc**: `"sc"`

Defined in: [utils/interfaces.ts:196](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L196)

***

### sd

> **sd**: `"sd"`

Defined in: [utils/interfaces.ts:197](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L197)

***

### se

> **se**: `"se"`

Defined in: [utils/interfaces.ts:198](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L198)

***

### sg

> **sg**: `"sg"`

Defined in: [utils/interfaces.ts:199](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L199)

***

### sh

> **sh**: `"sh"`

Defined in: [utils/interfaces.ts:200](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L200)

***

### si

> **si**: `"si"`

Defined in: [utils/interfaces.ts:201](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L201)

***

### sj

> **sj**: `"sj"`

Defined in: [utils/interfaces.ts:202](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L202)

***

### sk

> **sk**: `"sk"`

Defined in: [utils/interfaces.ts:203](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L203)

***

### sl

> **sl**: `"sl"`

Defined in: [utils/interfaces.ts:204](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L204)

***

### sm

> **sm**: `"sm"`

Defined in: [utils/interfaces.ts:205](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L205)

***

### sn

> **sn**: `"sn"`

Defined in: [utils/interfaces.ts:206](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L206)

***

### so

> **so**: `"so"`

Defined in: [utils/interfaces.ts:207](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L207)

***

### sr

> **sr**: `"sr"`

Defined in: [utils/interfaces.ts:208](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L208)

***

### ss

> **ss**: `"ss"`

Defined in: [utils/interfaces.ts:209](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L209)

***

### st

> **st**: `"st"`

Defined in: [utils/interfaces.ts:210](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L210)

***

### sv

> **sv**: `"sv"`

Defined in: [utils/interfaces.ts:211](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L211)

***

### sx

> **sx**: `"sx"`

Defined in: [utils/interfaces.ts:212](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L212)

***

### sy

> **sy**: `"sy"`

Defined in: [utils/interfaces.ts:213](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L213)

***

### sz

> **sz**: `"sz"`

Defined in: [utils/interfaces.ts:214](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L214)

***

### tc

> **tc**: `"tc"`

Defined in: [utils/interfaces.ts:215](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L215)

***

### td

> **td**: `"td"`

Defined in: [utils/interfaces.ts:216](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L216)

***

### tf

> **tf**: `"tf"`

Defined in: [utils/interfaces.ts:217](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L217)

***

### tg

> **tg**: `"tg"`

Defined in: [utils/interfaces.ts:218](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L218)

***

### th

> **th**: `"th"`

Defined in: [utils/interfaces.ts:219](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L219)

***

### tj

> **tj**: `"tj"`

Defined in: [utils/interfaces.ts:220](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L220)

***

### tk

> **tk**: `"tk"`

Defined in: [utils/interfaces.ts:221](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L221)

***

### tl

> **tl**: `"tl"`

Defined in: [utils/interfaces.ts:222](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L222)

***

### tm

> **tm**: `"tm"`

Defined in: [utils/interfaces.ts:223](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L223)

***

### tn

> **tn**: `"tn"`

Defined in: [utils/interfaces.ts:224](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L224)

***

### to

> **to**: `"to"`

Defined in: [utils/interfaces.ts:225](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L225)

***

### tr

> **tr**: `"tr"`

Defined in: [utils/interfaces.ts:226](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L226)

***

### tt

> **tt**: `"tt"`

Defined in: [utils/interfaces.ts:227](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L227)

***

### tv

> **tv**: `"tv"`

Defined in: [utils/interfaces.ts:228](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L228)

***

### tw

> **tw**: `"tw"`

Defined in: [utils/interfaces.ts:229](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L229)

***

### tz

> **tz**: `"tz"`

Defined in: [utils/interfaces.ts:230](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L230)

***

### ua

> **ua**: `"ua"`

Defined in: [utils/interfaces.ts:231](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L231)

***

### ug

> **ug**: `"ug"`

Defined in: [utils/interfaces.ts:232](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L232)

***

### um

> **um**: `"um"`

Defined in: [utils/interfaces.ts:233](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L233)

***

### us

> **us**: `"us"`

Defined in: [utils/interfaces.ts:234](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L234)

***

### uy

> **uy**: `"uy"`

Defined in: [utils/interfaces.ts:235](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L235)

***

### uz

> **uz**: `"uz"`

Defined in: [utils/interfaces.ts:236](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L236)

***

### va

> **va**: `"va"`

Defined in: [utils/interfaces.ts:237](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L237)

***

### vc

> **vc**: `"vc"`

Defined in: [utils/interfaces.ts:238](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L238)

***

### ve

> **ve**: `"ve"`

Defined in: [utils/interfaces.ts:239](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L239)

***

### vg

> **vg**: `"vg"`

Defined in: [utils/interfaces.ts:240](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L240)

***

### vi

> **vi**: `"vi"`

Defined in: [utils/interfaces.ts:241](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L241)

***

### vn

> **vn**: `"vn"`

Defined in: [utils/interfaces.ts:242](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L242)

***

### vu

> **vu**: `"vu"`

Defined in: [utils/interfaces.ts:243](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L243)

***

### wf

> **wf**: `"wf"`

Defined in: [utils/interfaces.ts:244](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L244)

***

### ws

> **ws**: `"ws"`

Defined in: [utils/interfaces.ts:245](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L245)

***

### ye

> **ye**: `"ye"`

Defined in: [utils/interfaces.ts:246](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L246)

***

### yt

> **yt**: `"yt"`

Defined in: [utils/interfaces.ts:247](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L247)

***

### za

> **za**: `"za"`

Defined in: [utils/interfaces.ts:248](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L248)

***

### zm

> **zm**: `"zm"`

Defined in: [utils/interfaces.ts:249](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L249)

***

### zw

> **zw**: `"zw"`

Defined in: [utils/interfaces.ts:250](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L250)
