[Admin Docs](/)

***

# Interface: InterfaceQueryOrganizationEventListItem

Defined in: [utils/interfaces.ts:1121](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1121)

## Extends

- [`InterfaceBaseEvent`](InterfaceBaseEvent.md)

## Properties

### \_id

> **\_id**: `string`

Defined in: [utils/interfaces.ts:342](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L342)

#### Inherited from

[`InterfaceBaseEvent`](InterfaceBaseEvent.md).[`_id`](InterfaceBaseEvent.md#_id)

***

### allDay

> **allDay**: `boolean`

Defined in: [utils/interfaces.ts:350](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L350)

#### Inherited from

[`InterfaceBaseEvent`](InterfaceBaseEvent.md).[`allDay`](InterfaceBaseEvent.md#allday)

***

### description

> **description**: `string`

Defined in: [utils/interfaces.ts:344](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L344)

#### Inherited from

[`InterfaceBaseEvent`](InterfaceBaseEvent.md).[`description`](InterfaceBaseEvent.md#description)

***

### endDate

> **endDate**: `string`

Defined in: [utils/interfaces.ts:346](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L346)

#### Inherited from

[`InterfaceBaseEvent`](InterfaceBaseEvent.md).[`endDate`](InterfaceBaseEvent.md#enddate)

***

### endTime

> **endTime**: `string`

Defined in: [utils/interfaces.ts:349](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L349)

#### Inherited from

[`InterfaceBaseEvent`](InterfaceBaseEvent.md).[`endTime`](InterfaceBaseEvent.md#endtime)

***

### isPublic

> **isPublic**: `boolean`

Defined in: [utils/interfaces.ts:1123](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1123)

***

### isRegisterable

> **isRegisterable**: `boolean`

Defined in: [utils/interfaces.ts:1124](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1124)

***

### location

> **location**: `string`

Defined in: [utils/interfaces.ts:347](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L347)

#### Inherited from

[`InterfaceBaseEvent`](InterfaceBaseEvent.md).[`location`](InterfaceBaseEvent.md#location)

***

### recurring

> **recurring**: `boolean`

Defined in: [utils/interfaces.ts:351](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L351)

#### Inherited from

[`InterfaceBaseEvent`](InterfaceBaseEvent.md).[`recurring`](InterfaceBaseEvent.md#recurring)

***

### startDate

> **startDate**: `string`

Defined in: [utils/interfaces.ts:345](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L345)

#### Inherited from

[`InterfaceBaseEvent`](InterfaceBaseEvent.md).[`startDate`](InterfaceBaseEvent.md#startdate)

***

### startTime

> **startTime**: `string`

Defined in: [utils/interfaces.ts:348](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L348)

#### Inherited from

[`InterfaceBaseEvent`](InterfaceBaseEvent.md).[`startTime`](InterfaceBaseEvent.md#starttime)

***

### title

> **title**: `string`

Defined in: [utils/interfaces.ts:343](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L343)

#### Inherited from

[`InterfaceBaseEvent`](InterfaceBaseEvent.md).[`title`](InterfaceBaseEvent.md#title)
