[Admin Docs](/)

***

# Interface: InterfaceUserPg

Defined in: [utils/interfaces.ts:504](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L504)

## Properties

### addressLine1

> **addressLine1**: `string`

Defined in: [utils/interfaces.ts:505](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L505)

***

### addressLine2

> **addressLine2**: `string`

Defined in: [utils/interfaces.ts:506](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L506)

***

### avatarMimeType

> **avatarMimeType**: `string`

Defined in: [utils/interfaces.ts:507](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L507)

***

### avatarURL

> **avatarURL**: `string`

Defined in: [utils/interfaces.ts:508](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L508)

***

### birthDate

> **birthDate**: `Date`

Defined in: [utils/interfaces.ts:509](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L509)

***

### city

> **city**: `string`

Defined in: [utils/interfaces.ts:510](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L510)

***

### countryCode

> **countryCode**: [`Iso3166Alpha2CountryCode`](../enumerations/Iso3166Alpha2CountryCode.md)

Defined in: [utils/interfaces.ts:511](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L511)

***

### createdAt

> **createdAt**: `string`

Defined in: [utils/interfaces.ts:512](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L512)

***

### creator

> **creator**: `InterfaceUserPg`

Defined in: [utils/interfaces.ts:513](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L513)

***

### description

> **description**: `string`

Defined in: [utils/interfaces.ts:514](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L514)

***

### educationGrade

> **educationGrade**: `UserEducationGrade`

Defined in: [utils/interfaces.ts:515](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L515)

***

### emailAddress

> **emailAddress**: `string`

Defined in: [utils/interfaces.ts:516](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L516)

***

### employmentStatus

> **employmentStatus**: `UserEmploymentStatus`

Defined in: [utils/interfaces.ts:517](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L517)

***

### homePhoneNumber

> **homePhoneNumber**: `string`

Defined in: [utils/interfaces.ts:518](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L518)

***

### id

> **id**: `ID`

Defined in: [utils/interfaces.ts:519](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L519)

***

### isEmailAddressVerified

> **isEmailAddressVerified**: `boolean`

Defined in: [utils/interfaces.ts:520](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L520)

***

### maritalStatus

> **maritalStatus**: `UserMaritalStatus`

Defined in: [utils/interfaces.ts:521](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L521)

***

### mobilePhoneNumber

> **mobilePhoneNumber**: `string`

Defined in: [utils/interfaces.ts:522](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L522)

***

### name

> **name**: `string`

Defined in: [utils/interfaces.ts:523](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L523)

***

### natalSex

> **natalSex**: `UserNatalSex`

Defined in: [utils/interfaces.ts:524](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L524)

***

### postalCode

> **postalCode**: `string`

Defined in: [utils/interfaces.ts:525](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L525)

***

### role

> **role**: `UserRole`

Defined in: [utils/interfaces.ts:526](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L526)

***

### state

> **state**: `string`

Defined in: [utils/interfaces.ts:527](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L527)

***

### updatedAt

> **updatedAt**: `string`

Defined in: [utils/interfaces.ts:528](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L528)

***

### updater

> **updater**: `InterfaceUserPg`

Defined in: [utils/interfaces.ts:529](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L529)

***

### workPhoneNumber

> **workPhoneNumber**: `string`

Defined in: [utils/interfaces.ts:530](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L530)
