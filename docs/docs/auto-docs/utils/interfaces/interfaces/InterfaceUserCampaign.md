[Admin Docs](/)

***

# Interface: InterfaceUserCampaign

Defined in: [utils/interfaces.ts:1011](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1011)

## Properties

### \_id

> **\_id**: `string`

Defined in: [utils/interfaces.ts:1012](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1012)

***

### currency

> **currency**: `string`

Defined in: [utils/interfaces.ts:1017](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1017)

***

### endDate

> **endDate**: `Date`

Defined in: [utils/interfaces.ts:1016](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1016)

***

### fundingGoal

> **fundingGoal**: `number`

Defined in: [utils/interfaces.ts:1014](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1014)

***

### name

> **name**: `string`

Defined in: [utils/interfaces.ts:1013](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1013)

***

### startDate

> **startDate**: `Date`

Defined in: [utils/interfaces.ts:1015](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1015)
