[Admin Docs](/)

***

# Interface: InterfaceQueryOrganizationListObject

Defined in: [utils/interfaces.ts:848](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L848)

## Properties

### \_id

> **\_id**: `string`

Defined in: [utils/interfaces.ts:849](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L849)

***

### address

> **address**: [`InterfaceAddress`](InterfaceAddress.md)

Defined in: [utils/interfaces.ts:863](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L863)

***

### admins

> **admins**: `object`[]

Defined in: [utils/interfaces.ts:859](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L859)

#### \_id

> **\_id**: `string`

***

### createdAt

> **createdAt**: `string`

Defined in: [utils/interfaces.ts:862](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L862)

***

### creator

> **creator**: `object`

Defined in: [utils/interfaces.ts:851](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L851)

#### firstName

> **firstName**: `string`

#### lastName

> **lastName**: `string`

***

### image

> **image**: `string`

Defined in: [utils/interfaces.ts:850](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L850)

***

### members

> **members**: `object`[]

Defined in: [utils/interfaces.ts:856](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L856)

#### \_id

> **\_id**: `string`

***

### name

> **name**: `string`

Defined in: [utils/interfaces.ts:855](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L855)
