[Admin Docs](/)

***

# Interface: InterfacePostCard

Defined in: [utils/interfaces.ts:1212](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1212)

## Properties

### commentCount

> **commentCount**: `number`

Defined in: [utils/interfaces.ts:1226](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1226)

***

### comments

> **comments**: `object`[]

Defined in: [utils/interfaces.ts:1227](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1227)

#### creator

> **creator**: `object`

##### creator.email

> **email**: `string`

##### creator.firstName

> **firstName**: `string`

##### creator.id

> **id**: `string`

##### creator.lastName

> **lastName**: `string`

#### id

> **id**: `string`

#### likeCount

> **likeCount**: `number`

#### likedBy

> **likedBy**: `object`[]

#### text

> **text**: `string`

***

### creator

> **creator**: `object`

Defined in: [utils/interfaces.ts:1214](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1214)

#### email

> **email**: `string`

#### firstName

> **firstName**: `string`

#### id

> **id**: `string`

#### lastName

> **lastName**: `string`

***

### fetchPosts()

> **fetchPosts**: () => `void`

Defined in: [utils/interfaces.ts:1246](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1246)

#### Returns

`void`

***

### id

> **id**: `string`

Defined in: [utils/interfaces.ts:1213](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1213)

***

### image

> **image**: `string`

Defined in: [utils/interfaces.ts:1221](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1221)

***

### likeCount

> **likeCount**: `number`

Defined in: [utils/interfaces.ts:1225](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1225)

***

### likedBy

> **likedBy**: `object`[]

Defined in: [utils/interfaces.ts:1241](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1241)

#### firstName

> **firstName**: `string`

#### id

> **id**: `string`

#### lastName

> **lastName**: `string`

***

### postedAt

> **postedAt**: `string`

Defined in: [utils/interfaces.ts:1220](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1220)

***

### text

> **text**: `string`

Defined in: [utils/interfaces.ts:1223](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1223)

***

### title

> **title**: `string`

Defined in: [utils/interfaces.ts:1224](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1224)

***

### video

> **video**: `string`

Defined in: [utils/interfaces.ts:1222](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1222)
