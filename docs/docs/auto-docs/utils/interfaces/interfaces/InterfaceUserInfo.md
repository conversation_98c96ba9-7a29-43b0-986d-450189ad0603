[Admin Docs](/)

***

# Interface: InterfaceUserInfo

Defined in: [utils/interfaces.ts:333](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L333)

## Properties

### \_id

> **\_id**: `string`

Defined in: [utils/interfaces.ts:336](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L336)

***

### firstName

> **firstName**: `string`

Defined in: [utils/interfaces.ts:334](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L334)

***

### image?

> `optional` **image**: `string`

Defined in: [utils/interfaces.ts:337](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L337)

***

### lastName

> **lastName**: `string`

Defined in: [utils/interfaces.ts:335](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L335)
