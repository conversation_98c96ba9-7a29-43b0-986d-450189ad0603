[Admin Docs](/)

***

# Interface: InterfaceQueryFundCampaignsPledges

Defined in: [utils/interfaces.ts:1019](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1019)

## Properties

### currencyCode

> **currencyCode**: `string`

Defined in: [utils/interfaces.ts:1025](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1025)

***

### endAt

> **endAt**: `Date`

Defined in: [utils/interfaces.ts:1027](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1027)

***

### fundId

> **fundId**: `object`

Defined in: [utils/interfaces.ts:1020](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1020)

#### name

> **name**: `string`

***

### goalAmount

> **goalAmount**: `number`

Defined in: [utils/interfaces.ts:1024](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1024)

***

### name

> **name**: `string`

Defined in: [utils/interfaces.ts:1023](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1023)

***

### pledges

> **pledges**: `object`

Defined in: [utils/interfaces.ts:1028](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1028)

#### edges

> **edges**: `object`[]

***

### startAt

> **startAt**: `Date`

Defined in: [utils/interfaces.ts:1026](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1026)
