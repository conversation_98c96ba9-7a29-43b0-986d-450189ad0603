[Admin Docs](/)

***

# Interface: InterfaceUserType

Defined in: [utils/interfaces.ts:306](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L306)

## Properties

### user

> **user**: `object`

Defined in: [utils/interfaces.ts:307](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L307)

#### email

> **email**: `string`

#### firstName

> **firstName**: `string`

#### image

> **image**: `string`

#### lastName

> **lastName**: `string`
