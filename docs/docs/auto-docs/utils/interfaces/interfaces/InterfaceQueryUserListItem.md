[Admin Docs](/)

***

# Interface: InterfaceQueryUserListItem

Defined in: [utils/interfaces.ts:1137](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1137)

## Properties

### appUserProfile

> **appUserProfile**: `object`

Defined in: [utils/interfaces.ts:1176](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1176)

#### \_id

> **\_id**: `string`

#### adminFor

> **adminFor**: `object`[]

#### createdEvents

> **createdEvents**: `object`[]

#### createdOrganizations

> **createdOrganizations**: `object`[]

#### eventAdmin

> **eventAdmin**: `object`[]

#### isSuperAdmin

> **isSuperAdmin**: `boolean`

***

### user

> **user**: `object`

Defined in: [utils/interfaces.ts:1138](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1138)

#### \_id

> **\_id**: `string`

#### createdAt

> **createdAt**: `string`

#### email

> **email**: `string`

#### firstName

> **firstName**: `string`

#### image

> **image**: `string`

#### joinedOrganizations

> **joinedOrganizations**: `object`[]

#### lastName

> **lastName**: `string`

#### membershipRequests

> **membershipRequests**: `object`[]

#### organizationsBlockedBy

> **organizationsBlockedBy**: `object`[]

#### registeredEvents

> **registeredEvents**: `object`[]
