[Admin Docs](/)

***

# Interface: InterfacePageInfoPg

Defined in: [utils/interfaces.ts:498](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L498)

## Properties

### endCursor

> **endCursor**: `string`

Defined in: [utils/interfaces.ts:499](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L499)

***

### hasNextPage

> **hasNextPage**: `boolean`

Defined in: [utils/interfaces.ts:500](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L500)

***

### hasPreviousPage

> **hasPreviousPage**: `boolean`

Defined in: [utils/interfaces.ts:501](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L501)

***

### startCursor

> **startCursor**: `string`

Defined in: [utils/interfaces.ts:502](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L502)
