[Admin Docs](/)

***

# Interface: InterfaceQueryUserTagsAssignedMembers

Defined in: [utils/interfaces.ts:956](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L956)

## Properties

### ancestorTags

> **ancestorTags**: `object`[]

Defined in: [utils/interfaces.ts:959](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L959)

#### \_id

> **\_id**: `string`

#### name

> **name**: `string`

***

### name

> **name**: `string`

Defined in: [utils/interfaces.ts:957](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L957)

***

### usersAssignedTo

> **usersAssignedTo**: `InterfaceTagMembersData`

Defined in: [utils/interfaces.ts:958](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L958)
