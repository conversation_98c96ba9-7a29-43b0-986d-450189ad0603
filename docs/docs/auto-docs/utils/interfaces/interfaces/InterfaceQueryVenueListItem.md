[Admin Docs](/)

***

# Interface: InterfaceQueryVenueListItem

Defined in: [utils/interfaces.ts:1186](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1186)

## Properties

### \_id

> **\_id**: `string`

Defined in: [utils/interfaces.ts:1187](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1187)

***

### capacity

> **capacity**: `string`

Defined in: [utils/interfaces.ts:1191](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1191)

***

### description

> **description**: `string`

Defined in: [utils/interfaces.ts:1189](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1189)

***

### image

> **image**: `string`

Defined in: [utils/interfaces.ts:1190](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1190)

***

### name

> **name**: `string`

Defined in: [utils/interfaces.ts:1188](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1188)
