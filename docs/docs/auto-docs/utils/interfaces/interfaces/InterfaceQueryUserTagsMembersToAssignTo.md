[Admin Docs](/)

***

# Interface: InterfaceQueryUserTagsMembersToAssignTo

Defined in: [utils/interfaces.ts:965](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L965)

## Properties

### name

> **name**: `string`

Defined in: [utils/interfaces.ts:966](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L966)

***

### usersToAssignTo

> **usersToAssignTo**: `InterfaceTagMembersData`

Defined in: [utils/interfaces.ts:967](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L967)
