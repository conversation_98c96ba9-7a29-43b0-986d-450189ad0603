[Admin Docs](/)

***

# Interface: InterfaceVolunteer<PERSON>ank

Defined in: [utils/interfaces.ts:1434](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1434)

## Properties

### hoursVolunteered

> **hoursVolunteered**: `number`

Defined in: [utils/interfaces.ts:1436](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1436)

***

### rank

> **rank**: `number`

Defined in: [utils/interfaces.ts:1435](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1435)

***

### user

> **user**: `object`

Defined in: [utils/interfaces.ts:1437](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1437)

#### \_id

> **\_id**: `string`

#### email

> **email**: `string`

#### firstName

> **firstName**: `string`

#### image

> **image**: `string`

#### lastName

> **lastName**: `string`
