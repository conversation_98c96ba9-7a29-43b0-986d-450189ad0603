[Admin Docs](/)

***

# Interface: InterfacePostForm

Defined in: [utils/interfaces.ts:866](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L866)

## Properties

### pinned

> **pinned**: `boolean`

Defined in: [utils/interfaces.ts:871](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L871)

***

### postinfo

> **postinfo**: `string`

Defined in: [utils/interfaces.ts:868](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L868)

***

### postphoto

> **postphoto**: `string`

Defined in: [utils/interfaces.ts:869](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L869)

***

### posttitle

> **posttitle**: `string`

Defined in: [utils/interfaces.ts:867](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L867)

***

### postvideo

> **postvideo**: `string`

Defined in: [utils/interfaces.ts:870](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L870)
