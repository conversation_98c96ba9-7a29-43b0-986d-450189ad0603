[Admin Docs](/)

***

# Interface: InterfaceUserTypePG

Defined in: [utils/interfaces.ts:315](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L315)

## Properties

### user

> **user**: `object`

Defined in: [utils/interfaces.ts:316](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L316)

#### emailAddress

> **emailAddress**: `string`

#### id

> **id**: `string`

#### name

> **name**: `string`

#### role

> **role**: `string`
