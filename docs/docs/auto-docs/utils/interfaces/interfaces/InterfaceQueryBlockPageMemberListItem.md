[Admin Docs](/)

***

# Interface: InterfaceQueryBlockPageMemberListItem

Defined in: [utils/interfaces.ts:1127](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1127)

## Properties

### \_id

> **\_id**: `string`

Defined in: [utils/interfaces.ts:1128](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1128)

***

### email

> **email**: `string`

Defined in: [utils/interfaces.ts:1131](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1131)

***

### firstName

> **firstName**: `string`

Defined in: [utils/interfaces.ts:1129](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1129)

***

### lastName

> **lastName**: `string`

Defined in: [utils/interfaces.ts:1130](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1130)

***

### organizationsBlockedBy

> **organizationsBlockedBy**: `object`[]

Defined in: [utils/interfaces.ts:1132](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1132)

#### \_id

> **\_id**: `string`
