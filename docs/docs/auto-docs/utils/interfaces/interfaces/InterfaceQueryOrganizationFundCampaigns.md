[Admin Docs](/)

***

# Interface: InterfaceQueryOrganizationFundCampaigns

Defined in: [utils/interfaces.ts:993](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L993)

## Properties

### campaigns

> **campaigns**: `object`

Defined in: [utils/interfaces.ts:997](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L997)

#### edges

> **edges**: `object`[]

***

### id

> **id**: `string`

Defined in: [utils/interfaces.ts:994](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L994)

***

### isArchived

> **isArchived**: `boolean`

Defined in: [utils/interfaces.ts:996](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L996)

***

### name

> **name**: `string`

Defined in: [utils/interfaces.ts:995](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L995)
