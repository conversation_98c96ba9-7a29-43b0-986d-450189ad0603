[Admin Docs](/)

***

# Interface: InterfaceUserInfo\_PG

Defined in: [utils/interfaces.ts:1113](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1113)

## Properties

### firstName

> **firstName**: `string`

Defined in: [utils/interfaces.ts:1114](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1114)

***

### id

> **id**: `string`

Defined in: [utils/interfaces.ts:1117](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1117)

***

### image?

> `optional` **image**: `string`

Defined in: [utils/interfaces.ts:1118](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1118)

***

### lastName

> **lastName**: `string`

Defined in: [utils/interfaces.ts:1115](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1115)

***

### name

> **name**: `string`

Defined in: [utils/interfaces.ts:1116](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/interfaces.ts#L1116)
