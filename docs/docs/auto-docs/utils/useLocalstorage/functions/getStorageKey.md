[Admin Docs](/)

***

# Function: getStorageKey()

> **getStorageKey**(`prefix`, `key`): `string`

Defined in: [utils/useLocalstorage.ts:19](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/useLocalstorage.ts#L19)

Generates the prefixed key for storage.

## Parameters

### prefix

`string`

Prefix to be added to the key, common for all keys.

### key

`string`

The unique name identifying the value.

## Returns

`string`

- Prefixed key.
