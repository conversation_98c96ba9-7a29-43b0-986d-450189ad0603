[Admin Docs](/)

***

# Function: removeItem()

> **removeItem**(`prefix`, `key`): `void`

Defined in: [utils/useLocalstorage.ts:51](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/utils/useLocalstorage.ts#L51)

Removes the value associated with the given key from local storage.

## Parameters

### prefix

`string`

Prefix to be added to the key, common for all keys.

### key

`string`

The unique name identifying the value.

## Returns

`void`
