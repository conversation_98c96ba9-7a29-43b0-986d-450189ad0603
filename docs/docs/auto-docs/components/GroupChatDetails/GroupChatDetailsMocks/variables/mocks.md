[Admin Docs](/)

***

# Variable: mocks

> `const` **mocks**: (\{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `chatId`: `undefined`; `firstName_contains`: `string`; `input`: `undefined`; `lastName_contains`: `string`; `userId`: `undefined`; \}; \}; `result`: \{ `data`: \{ `addUserToGroupChat`: `undefined`; `updateChat`: `undefined`; `users`: `object`[]; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `chatId`: `string`; `firstName_contains`: `undefined`; `input`: `undefined`; `lastName_contains`: `undefined`; `userId`: `string`; \}; \}; `result`: \{ `data`: \{ `addUserToGroupChat`: \{ `_id`: `string`; `success`: `boolean`; \}; `updateChat`: `undefined`; `users`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `chatId`: `undefined`; `firstName_contains`: `undefined`; `input`: \{ `_id`: `string`; `image`: `string`; `name`: `string`; \}; `lastName_contains`: `undefined`; `userId`: `undefined`; \}; \}; `result`: \{ `data`: \{ `addUserToGroupChat`: `undefined`; `updateChat`: \{ `_id`: `string`; `success`: `boolean`; \}; `users`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `chatId`: `undefined`; `firstName_contains`: `undefined`; `input`: `undefined`; `lastName_contains`: `undefined`; `userId`: `undefined`; \}; \}; `result`: \{ `data`: \{ `addUserToGroupChat`: `undefined`; `updateChat`: \{ `_id`: `string`; `success`: `boolean`; \}; `users`: `undefined`; \}; \}; \})[]

Defined in: [components/GroupChatDetails/GroupChatDetailsMocks.tsx:109](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/GroupChatDetails/GroupChatDetailsMocks.tsx#L109)
