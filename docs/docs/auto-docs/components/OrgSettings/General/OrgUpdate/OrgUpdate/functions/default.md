[Admin Docs](/)

***

# Function: default()

> **default**(`props`): `Element`

Defined in: [components/OrgSettings/General/OrgUpdate/OrgUpdate.tsx:46](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/OrgSettings/General/OrgUpdate/OrgUpdate.tsx#L46)

Component for updating organization details.

This component allows users to update the organization's name, description, address,
visibility settings, and upload an image. It uses GraphQL mutations and queries to
fetch and update data.

## Parameters

### props

`InterfaceOrgUpdateProps`

Component props containing the organization ID.

## Returns

`Element`

The rendered component.
