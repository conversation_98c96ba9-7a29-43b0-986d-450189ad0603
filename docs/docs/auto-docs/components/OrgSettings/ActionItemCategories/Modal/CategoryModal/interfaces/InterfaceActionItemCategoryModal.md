[Admin Docs](/)

***

# Interface: InterfaceActionItemCategoryModal

Defined in: [components/OrgSettings/ActionItemCategories/Modal/CategoryModal.tsx:50](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/OrgSettings/ActionItemCategories/Modal/CategoryModal.tsx#L50)

## Properties

### category

> **category**: [`InterfaceActionItemCategoryInfo`](../../../../../../utils/interfaces/interfaces/InterfaceActionItemCategoryInfo.md)

Defined in: [components/OrgSettings/ActionItemCategories/Modal/CategoryModal.tsx:55](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/OrgSettings/ActionItemCategories/Modal/CategoryModal.tsx#L55)

***

### hide()

> **hide**: () => `void`

Defined in: [components/OrgSettings/ActionItemCategories/Modal/CategoryModal.tsx:52](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/OrgSettings/ActionItemCategories/Modal/CategoryModal.tsx#L52)

#### Returns

`void`

***

### isOpen

> **isOpen**: `boolean`

Defined in: [components/OrgSettings/ActionItemCategories/Modal/CategoryModal.tsx:51](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/OrgSettings/ActionItemCategories/Modal/CategoryModal.tsx#L51)

***

### mode

> **mode**: `"create"` \| `"edit"`

Defined in: [components/OrgSettings/ActionItemCategories/Modal/CategoryModal.tsx:56](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/OrgSettings/ActionItemCategories/Modal/CategoryModal.tsx#L56)

***

### orgId

> **orgId**: `string`

Defined in: [components/OrgSettings/ActionItemCategories/Modal/CategoryModal.tsx:54](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/OrgSettings/ActionItemCategories/Modal/CategoryModal.tsx#L54)

***

### refetchCategories()

> **refetchCategories**: () => `void`

Defined in: [components/OrgSettings/ActionItemCategories/Modal/CategoryModal.tsx:53](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/OrgSettings/ActionItemCategories/Modal/CategoryModal.tsx#L53)

#### Returns

`void`
