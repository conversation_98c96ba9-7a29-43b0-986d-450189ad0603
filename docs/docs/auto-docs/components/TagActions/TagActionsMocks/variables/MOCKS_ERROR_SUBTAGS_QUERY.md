[Admin Docs](/)

***

# Variable: MOCKS\_ERROR\_SUBTAGS\_QUERY

> `const` **MOCKS\_ERROR\_SUBTAGS\_QUERY**: (\{ `error`: `undefined`; `request`: \{ `query`: `DocumentNode`; `variables`: \{ `first`: `number`; `id`: `string`; `where`: \{ `name`: \{ `starts_with`: `string`; \}; \}; \}; \}; `result`: \{ `data`: \{ `organizations`: `object`[]; \}; \}; \} \| \{ `error`: `Error`; `request`: \{ `query`: `any`; `variables`: \{ `first`: `number`; `id`: `string`; `where`: `undefined`; \}; \}; `result`: `undefined`; \})[]

Defined in: [components/TagActions/TagActionsMocks.ts:293](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/TagActions/TagActionsMocks.ts#L293)
