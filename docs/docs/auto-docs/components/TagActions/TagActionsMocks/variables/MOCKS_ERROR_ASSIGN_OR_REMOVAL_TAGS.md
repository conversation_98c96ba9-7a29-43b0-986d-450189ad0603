[Admin Docs](/)

***

# Variable: MOCKS\_ERROR\_ASSIGN\_OR\_REMOVAL\_TAGS

> `const` **MOCKS\_ERROR\_ASSIGN\_OR\_REMOVAL\_TAGS**: (\{ `error`: `undefined`; `request`: \{ `query`: `DocumentNode`; `variables`: \{ `currentTagId`: `undefined`; `first`: `number`; `id`: `string`; `selectedTagIds`: `undefined`; `where`: \{ `name`: \{ `starts_with`: `string`; \}; \}; \}; \}; `result`: \{ `data`: \{ `organizations`: `object`[]; \}; \}; \} \| \{ `error`: `Error`; `request`: \{ `query`: `any`; `variables`: \{ `currentTagId`: `string`; `first`: `undefined`; `id`: `undefined`; `selectedTagIds`: `string`[]; `where`: `undefined`; \}; \}; `result`: `undefined`; \})[]

Defined in: [components/TagActions/TagActionsMocks.ts:337](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/TagActions/TagActionsMocks.ts#L337)
