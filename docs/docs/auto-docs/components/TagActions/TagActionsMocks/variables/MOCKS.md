[Admin <PERSON>s](/)

***

# Variable: MOCKS

> `const` **MOCKS**: (\{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `after`: `undefined`; `currentTagId`: `undefined`; `first`: `number`; `id`: `string`; `selectedTagIds`: `undefined`; `where`: \{ `name`: \{ `starts_with`: `string`; \}; \}; \}; \}; `result`: \{ `data`: \{ `assignToUserTags`: `undefined`; `getChildTags`: `undefined`; `organizations`: `object`[]; `removeFromUserTags`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `after`: `string`; `currentTagId`: `undefined`; `first`: `number`; `id`: `string`; `selectedTagIds`: `undefined`; `where`: \{ `name`: \{ `starts_with`: `string`; \}; \}; \}; \}; `result`: \{ `data`: \{ `assignToUserTags`: `undefined`; `getChildTags`: `undefined`; `organizations`: `object`[]; `removeFromUserTags`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `any`; `variables`: \{ `after`: `undefined`; `currentTagId`: `undefined`; `first`: `number`; `id`: `string`; `selectedTagIds`: `undefined`; `where`: `undefined`; \}; \}; `result`: \{ `data`: \{ `assignToUserTags`: `undefined`; `getChildTags`: \{ `ancestorTags`: `any`[]; `childTags`: \{ `edges`: `object`[]; `pageInfo`: \{ `endCursor`: `string`; `hasNextPage`: `boolean`; `hasPreviousPage`: `boolean`; `startCursor`: `string`; \}; `totalCount`: `number`; \}; `name`: `string`; \}; `organizations`: `undefined`; `removeFromUserTags`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `any`; `variables`: \{ `after`: `string`; `currentTagId`: `undefined`; `first`: `number`; `id`: `string`; `selectedTagIds`: `undefined`; `where`: `undefined`; \}; \}; `result`: \{ `data`: \{ `assignToUserTags`: `undefined`; `getChildTags`: \{ `ancestorTags`: `any`[]; `childTags`: \{ `edges`: `object`[]; `pageInfo`: \{ `endCursor`: `string`; `hasNextPage`: `boolean`; `hasPreviousPage`: `boolean`; `startCursor`: `string`; \}; `totalCount`: `number`; \}; `name`: `string`; \}; `organizations`: `undefined`; `removeFromUserTags`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `any`; `variables`: \{ `after`: `undefined`; `currentTagId`: `string`; `first`: `undefined`; `id`: `undefined`; `selectedTagIds`: `string`[]; `where`: `undefined`; \}; \}; `result`: \{ `data`: \{ `assignToUserTags`: \{ `_id`: `string`; \}; `getChildTags`: `undefined`; `organizations`: `undefined`; `removeFromUserTags`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `any`; `variables`: \{ `after`: `undefined`; `currentTagId`: `string`; `first`: `undefined`; `id`: `undefined`; `selectedTagIds`: `string`[]; `where`: `undefined`; \}; \}; `result`: \{ `data`: \{ `assignToUserTags`: `undefined`; `getChildTags`: `undefined`; `organizations`: `undefined`; `removeFromUserTags`: \{ `_id`: `string`; \}; \}; \}; \})[]

Defined in: [components/TagActions/TagActionsMocks.ts:104](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/TagActions/TagActionsMocks.ts#L104)
