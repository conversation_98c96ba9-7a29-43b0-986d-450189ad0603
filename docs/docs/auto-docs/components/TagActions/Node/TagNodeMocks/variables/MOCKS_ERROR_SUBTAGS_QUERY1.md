[Admin Docs](/)

***

# Variable: MOCKS\_ERROR\_SUBTAGS\_QUERY1

> `const` **MOCKS\_ERROR\_SUBTAGS\_QUERY1**: `object`[]

Defined in: [components/TagActions/Node/TagNodeMocks.ts:64](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/TagActions/Node/TagNodeMocks.ts#L64)

## Type declaration

### error

> **error**: `Error`

### request

> **request**: `object`

#### request.query

> **query**: `any` = `USER_TAG_SUB_TAGS`

#### request.variables

> **variables**: `object`

#### request.variables.first

> **first**: `number` = `10`

#### request.variables.id

> **id**: `string` = `'1'`
