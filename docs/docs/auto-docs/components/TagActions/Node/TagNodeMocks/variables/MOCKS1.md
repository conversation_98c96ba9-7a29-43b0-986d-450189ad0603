[Admin Docs](/)

***

# Variable: MOCKS1

> `const` **MOCKS1**: (\{ `request`: \{ `query`: `any`; `variables`: \{ `after`: `undefined`; `first`: `number`; `id`: `string`; \}; \}; `result`: \{ `data`: \{ `getChildTags`: \{ `__typename`: `string`; `childTags`: \{ `__typename`: `string`; `edges`: `object`[]; `pageInfo`: \{ `__typename`: `string`; `endCursor`: `string`; `hasNextPage`: `boolean`; \}; \}; \}; \}; \}; \} \| \{ `request`: \{ `query`: `any`; `variables`: \{ `after`: `string`; `first`: `number`; `id`: `string`; \}; \}; `result`: \{ `data`: \{ `getChildTags`: \{ `__typename`: `string`; `childTags`: \{ `__typename`: `string`; `edges`: `object`[]; `pageInfo`: \{ `__typename`: `string`; `endCursor`: `string`; `hasNextPage`: `boolean`; \}; \}; \}; \}; \}; \})[]

Defined in: [components/TagActions/Node/TagNodeMocks.ts:3](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/TagActions/Node/TagNodeMocks.ts#L3)
