[Admin Docs](/)

***

# Interface: InterfaceLeftDrawerProps

Defined in: [components/LeftDrawer/LeftDrawer.tsx:46](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/LeftDrawer/LeftDrawer.tsx#L46)

## Properties

### hideDrawer

> **hideDrawer**: `boolean`

Defined in: [components/LeftDrawer/LeftDrawer.tsx:47](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/LeftDrawer/LeftDrawer.tsx#L47)

***

### setHideDrawer

> **setHideDrawer**: `Dispatch`\<`SetStateAction`\<`boolean`\>\>

Defined in: [components/LeftDrawer/LeftDrawer.tsx:48](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/LeftDrawer/LeftDrawer.tsx#L48)
