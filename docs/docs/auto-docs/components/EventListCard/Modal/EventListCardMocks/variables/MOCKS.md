[Admin <PERSON>s](/)

***

# Variable: MOCKS

> `const` **MOCKS**: (\{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `allDay`: `undefined`; `description`: `undefined`; `endDate`: `undefined`; `endTime`: `undefined`; `eventId`: `undefined`; `frequency`: `undefined`; `id`: `string`; `interval`: `undefined`; `isPublic`: `undefined`; `isRegisterable`: `undefined`; `location`: `undefined`; `recurrenceEndDate`: `undefined`; `recurrenceStartDate`: `undefined`; `recurring`: `undefined`; `recurringEventDeleteType`: `undefined`; `recurringEventUpdateType`: `undefined`; `startDate`: `undefined`; `startTime`: `undefined`; `title`: `undefined`; `weekDayOccurenceInMonth`: `undefined`; `weekDays`: `undefined`; \}; \}; `result`: \{ `data`: \{ `registerForEvent`: `undefined`; `removeEvent`: \{ `_id`: `string`; \}; `updateEvent`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `allDay`: `undefined`; `description`: `undefined`; `endDate`: `undefined`; `endTime`: `undefined`; `eventId`: `undefined`; `frequency`: `undefined`; `id`: `string`; `interval`: `undefined`; `isPublic`: `undefined`; `isRegisterable`: `undefined`; `location`: `undefined`; `recurrenceEndDate`: `undefined`; `recurrenceStartDate`: `undefined`; `recurring`: `undefined`; `recurringEventDeleteType`: `string`; `recurringEventUpdateType`: `undefined`; `startDate`: `undefined`; `startTime`: `undefined`; `title`: `undefined`; `weekDayOccurenceInMonth`: `undefined`; `weekDays`: `undefined`; \}; \}; `result`: \{ `data`: \{ `registerForEvent`: `undefined`; `removeEvent`: \{ `_id`: `string`; \}; `updateEvent`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `allDay`: `boolean`; `description`: `string`; `endDate`: `string`; `endTime`: `undefined`; `eventId`: `undefined`; `frequency`: `undefined`; `id`: `string`; `interval`: `undefined`; `isPublic`: `boolean`; `isRegisterable`: `boolean`; `location`: `string`; `recurrenceEndDate`: `undefined`; `recurrenceStartDate`: `undefined`; `recurring`: `boolean`; `recurringEventDeleteType`: `undefined`; `recurringEventUpdateType`: `undefined`; `startDate`: `string`; `startTime`: `undefined`; `title`: `string`; `weekDayOccurenceInMonth`: `undefined`; `weekDays`: `undefined`; \}; \}; `result`: \{ `data`: \{ `registerForEvent`: `undefined`; `removeEvent`: `undefined`; `updateEvent`: \{ `_id`: `string`; \}; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `allDay`: `boolean`; `description`: `string`; `endDate`: `string`; `endTime`: `string`; `eventId`: `undefined`; `frequency`: `undefined`; `id`: `string`; `interval`: `undefined`; `isPublic`: `boolean`; `isRegisterable`: `boolean`; `location`: `string`; `recurrenceEndDate`: `undefined`; `recurrenceStartDate`: `undefined`; `recurring`: `boolean`; `recurringEventDeleteType`: `undefined`; `recurringEventUpdateType`: `undefined`; `startDate`: `string`; `startTime`: `string`; `title`: `string`; `weekDayOccurenceInMonth`: `undefined`; `weekDays`: `undefined`; \}; \}; `result`: \{ `data`: \{ `registerForEvent`: `undefined`; `removeEvent`: `undefined`; `updateEvent`: \{ `_id`: `string`; \}; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `allDay`: `boolean`; `description`: `string`; `endDate`: `string`; `endTime`: `undefined`; `eventId`: `undefined`; `frequency`: `string`; `id`: `string`; `interval`: `number`; `isPublic`: `boolean`; `isRegisterable`: `boolean`; `location`: `string`; `recurrenceEndDate`: `any`; `recurrenceStartDate`: `string`; `recurring`: `boolean`; `recurringEventDeleteType`: `undefined`; `recurringEventUpdateType`: `string`; `startDate`: `string`; `startTime`: `undefined`; `title`: `string`; `weekDayOccurenceInMonth`: `undefined`; `weekDays`: `string`[]; \}; \}; `result`: \{ `data`: \{ `registerForEvent`: `undefined`; `removeEvent`: `undefined`; `updateEvent`: \{ `_id`: `string`; \}; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `allDay`: `boolean`; `description`: `string`; `endDate`: `string`; `endTime`: `undefined`; `eventId`: `undefined`; `frequency`: `string`; `id`: `string`; `interval`: `number`; `isPublic`: `boolean`; `isRegisterable`: `boolean`; `location`: `string`; `recurrenceEndDate`: `string`; `recurrenceStartDate`: `string`; `recurring`: `boolean`; `recurringEventDeleteType`: `undefined`; `recurringEventUpdateType`: `string`; `startDate`: `string`; `startTime`: `undefined`; `title`: `string`; `weekDayOccurenceInMonth`: `number`; `weekDays`: `string`[]; \}; \}; `result`: \{ `data`: \{ `registerForEvent`: `undefined`; `removeEvent`: `undefined`; `updateEvent`: \{ `_id`: `string`; \}; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `allDay`: `boolean`; `description`: `string`; `endDate`: `string`; `endTime`: `undefined`; `eventId`: `undefined`; `frequency`: `string`; `id`: `string`; `interval`: `number`; `isPublic`: `boolean`; `isRegisterable`: `boolean`; `location`: `string`; `recurrenceEndDate`: `any`; `recurrenceStartDate`: `string`; `recurring`: `boolean`; `recurringEventDeleteType`: `undefined`; `recurringEventUpdateType`: `string`; `startDate`: `string`; `startTime`: `undefined`; `title`: `string`; `weekDayOccurenceInMonth`: `undefined`; `weekDays`: `undefined`; \}; \}; `result`: \{ `data`: \{ `registerForEvent`: `undefined`; `removeEvent`: `undefined`; `updateEvent`: \{ `_id`: `string`; \}; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `allDay`: `undefined`; `description`: `undefined`; `endDate`: `undefined`; `endTime`: `undefined`; `eventId`: `string`; `frequency`: `undefined`; `id`: `undefined`; `interval`: `undefined`; `isPublic`: `undefined`; `isRegisterable`: `undefined`; `location`: `undefined`; `recurrenceEndDate`: `undefined`; `recurrenceStartDate`: `undefined`; `recurring`: `undefined`; `recurringEventDeleteType`: `undefined`; `recurringEventUpdateType`: `undefined`; `startDate`: `undefined`; `startTime`: `undefined`; `title`: `undefined`; `weekDayOccurenceInMonth`: `undefined`; `weekDays`: `undefined`; \}; \}; `result`: \{ `data`: \{ `registerForEvent`: `object`[]; `removeEvent`: `undefined`; `updateEvent`: `undefined`; \}; \}; \})[]

Defined in: [components/EventListCard/Modal/EventListCardMocks.ts:7](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/EventListCard/Modal/EventListCardMocks.ts#L7)
