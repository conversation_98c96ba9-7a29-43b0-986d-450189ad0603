[Admin Docs](/)

***

# Variable: MOCKS\_ERROR\_MUTATIONS

> `const` **MOCKS\_ERROR\_MUTATIONS**: (\{ `error`: `Error`; `request`: \{ `query`: `any`; `variables`: \{ `deleteAgendaCategoryId`: `undefined`; `input`: \{ `description`: `string`; `name`: `string`; \}; `updateAgendaCategoryId`: `string`; \}; \}; \} \| \{ `error`: `Error`; `request`: \{ `query`: `any`; `variables`: \{ `deleteAgendaCategoryId`: `string`; `input`: `undefined`; `updateAgendaCategoryId`: `undefined`; \}; \}; \})[]

Defined in: [components/AgendaCategory/AgendaCategoryContainerMocks.ts:117](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/AgendaCategory/AgendaCategoryContainerMocks.ts#L117)
