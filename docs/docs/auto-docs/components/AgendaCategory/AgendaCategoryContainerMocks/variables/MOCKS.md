[Admin Docs](/)

***

# Variable: MOCKS

> `const` **MOCKS**: (\{ `request`: \{ `query`: `any`; `variables`: \{ `deleteAgendaCategoryId`: `undefined`; `input`: \{ `description`: `string`; `name`: `string`; \}; `updateAgendaCategoryId`: `string`; \}; \}; `result`: \{ `data`: \{ `deleteAgendaCategory`: `undefined`; `updateAgendaCategory`: \{ `_id`: `string`; \}; \}; \}; \} \| \{ `request`: \{ `query`: `any`; `variables`: \{ `deleteAgendaCategoryId`: `string`; `input`: `undefined`; `updateAgendaCategoryId`: `undefined`; \}; \}; `result`: \{ `data`: \{ `deleteAgendaCategory`: \{ `_id`: `string`; \}; `updateAgendaCategory`: `undefined`; \}; \}; \})[]

Defined in: [components/AgendaCategory/AgendaCategoryContainerMocks.ts:42](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/AgendaCategory/AgendaCategoryContainerMocks.ts#L42)
