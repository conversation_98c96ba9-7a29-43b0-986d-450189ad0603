[Admin Docs](/)

***

# Variable: MOCK\_VENUE\_ITEM\_LONG\_TEXT

> `const` **MOCK\_VENUE\_ITEM\_LONG\_TEXT**: `object`

Defined in: [components/Venues/VenueCardMocks.ts:19](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/Venues/VenueCardMocks.ts#L19)

## Type declaration

### \_id

> **\_id**: `string` = `'4'`

### capacity

> **capacity**: `string` = `'300'`

### description

> **description**: `string` = `'This is a very long description that should be truncated. It contains more than seventy five characters to ensure we can test the truncation logic properly. This text will be cut off.'`

### image

> **image**: `any` = `null`

### name

> **name**: `string` = `'This is a very long venue name that should definitely be truncated in the display'`
