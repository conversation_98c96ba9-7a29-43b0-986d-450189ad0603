[Admin Docs](/)

***

# Variable: MOCK\_VENUE\_ITEM\_WITH\_IMAGE

> `const` **MOCK\_VENUE\_ITEM\_WITH\_IMAGE**: `object`

Defined in: [components/Venues/VenueCardMocks.ts:11](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/Venues/VenueCardMocks.ts#L11)

## Type declaration

### \_id

> **\_id**: `string` = `'2'`

### capacity

> **capacity**: `string` = `'200'`

### description

> **description**: `string` = `'A modern conference room with all amenities.'`

### image

> **image**: `string` = `'https://surl.li/odyiad'`

### name

> **name**: `string` = `'Conference Room'`
