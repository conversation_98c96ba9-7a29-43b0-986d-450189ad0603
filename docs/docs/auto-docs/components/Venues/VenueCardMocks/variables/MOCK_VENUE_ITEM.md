[Admin Docs](/)

***

# Variable: MOCK\_VENUE\_ITEM

> `const` **MOCK\_VENUE\_ITEM**: `object`

Defined in: [components/Venues/VenueCardMocks.ts:3](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/Venues/VenueCardMocks.ts#L3)

## Type declaration

### \_id

> **\_id**: `string` = `'1'`

### capacity

> **capacity**: `string` = `'500'`

### description

> **description**: `string` = `'A spacious venue for large events and gatherings.'`

### image

> **image**: `any` = `null`

### name

> **name**: `string` = `'Grand Hall'`
