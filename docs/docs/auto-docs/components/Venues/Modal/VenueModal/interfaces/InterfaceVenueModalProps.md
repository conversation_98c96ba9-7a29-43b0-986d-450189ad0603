[Admin Docs](/)

***

# Interface: InterfaceVenueModalProps

Defined in: [components/Venues/Modal/VenueModal.tsx:49](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/Venues/Modal/VenueModal.tsx#L49)

## Properties

### edit

> **edit**: `boolean`

Defined in: [components/Venues/Modal/VenueModal.tsx:55](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/Venues/Modal/VenueModal.tsx#L55)

***

### onHide()

> **onHide**: () => `void`

Defined in: [components/Venues/Modal/VenueModal.tsx:51](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/Venues/Modal/VenueModal.tsx#L51)

#### Returns

`void`

***

### orgId

> **orgId**: `string`

Defined in: [components/Venues/Modal/VenueModal.tsx:53](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/Venues/Modal/VenueModal.tsx#L53)

***

### refetchVenues()

> **refetchVenues**: () => `void`

Defined in: [components/Venues/Modal/VenueModal.tsx:52](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/Venues/Modal/VenueModal.tsx#L52)

#### Returns

`void`

***

### show

> **show**: `boolean`

Defined in: [components/Venues/Modal/VenueModal.tsx:50](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/Venues/Modal/VenueModal.tsx#L50)

***

### venueData?

> `optional` **venueData**: [`InterfaceQueryVenueListItem`](../../../../../utils/interfaces/interfaces/InterfaceQueryVenueListItem.md)

Defined in: [components/Venues/Modal/VenueModal.tsx:54](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/Venues/Modal/VenueModal.tsx#L54)
