[Admin Docs](/)

***

# Variable: MOCKEVENT

> `const` **MOCKEVENT**: `object`

Defined in: [components/EventManagement/EventAttendance/EventAttendanceMocks.ts:3](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/EventManagement/EventAttendance/EventAttendanceMocks.ts#L3)

## Type declaration

### \_id

> **\_id**: `string` = `'event123'`

### allDay

> **allDay**: `boolean` = `false`

### attendees

> **attendees**: `object`[]

### baseRecurringEvent

> **baseRecurringEvent**: `object`

#### baseRecurringEvent.\_id

> **\_id**: `string` = `'recurringEvent123'`

### description

> **description**: `string` = `'This is a test event description'`

### endDate

> **endDate**: `string` = `'2023-05-02'`

### endTime

> **endTime**: `string` = `'17:00:00'`

### location

> **location**: `string` = `'Test Location'`

### organization

> **organization**: `object`

#### organization.\_id

> **\_id**: `string` = `'org456'`

#### organization.members

> **members**: `object`[]

### recurring

> **recurring**: `boolean` = `true`

### startDate

> **startDate**: `string` = `'2023-05-01'`

### startTime

> **startTime**: `string` = `'09:00:00'`

### title

> **title**: `string` = `'Test Event'`
