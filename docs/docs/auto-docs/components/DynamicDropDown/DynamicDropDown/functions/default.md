[Admin Docs](/)

***

# Function: default()

> **default**\<`T`\>(`__namedParameters`): `Element`

Defined in: [components/DynamicDropDown/DynamicDropDown.tsx:55](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/DynamicDropDown/DynamicDropDown.tsx#L55)

## Type Parameters

### T

`T` *extends* `Record`\<`string`, `unknown`\>

## Parameters

### \_\_namedParameters

`InterfaceChangeDropDownProps`\<`T`\>

## Returns

`Element`
