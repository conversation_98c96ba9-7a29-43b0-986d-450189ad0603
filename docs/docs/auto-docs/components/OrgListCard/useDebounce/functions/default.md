[Admin Docs](/)

***

# Function: default()

> **default**\<`T`\>(`callback`, `delay`): `object`

Defined in: [components/OrgListCard/useDebounce.tsx:31](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/OrgListCard/useDebounce.tsx#L31)

## Type Parameters

### T

`T` *extends* (...`args`) => `void`

## Parameters

### callback

`T`

### delay

`number`

## Returns

`object`

### cancel()

> **cancel**: () => `void`

#### Returns

`void`

### debouncedCallback()

> **debouncedCallback**: (...`args`) => `void`

#### Parameters

##### args

...`Parameters`\<`T`\>

#### Returns

`void`
