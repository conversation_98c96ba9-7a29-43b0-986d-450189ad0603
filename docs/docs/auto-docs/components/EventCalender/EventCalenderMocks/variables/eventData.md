[Admin Docs](/)

***

# Variable: eventData

> `const` **eventData**: (\{ `_id`: `string`; `allDay`: `boolean`; `attendees`: `any`[]; `creator`: \{\}; `description`: `string`; `endDate`: `string`; `endTime`: `string`; `isPublic`: `boolean`; `isRecurringEventException`: `boolean`; `isRegisterable`: `boolean`; `location`: `string`; `recurrenceRule`: `any`; `recurring`: `boolean`; `startDate`: `string`; `startTime`: `string`; `title`: `string`; `viewType`: [`ViewType`](../../../../screens/OrganizationEvents/OrganizationEvents/enumerations/ViewType.md); \} \| \{ `_id`: `string`; `allDay`: `boolean`; `attendees`: `any`[]; `creator`: \{\}; `description`: `string`; `endDate`: `string`; `endTime`: `string`; `isPublic`: `boolean`; `isRecurringEventException`: `boolean`; `isRegisterable`: `boolean`; `location`: `string`; `recurrenceRule`: `any`; `recurring`: `boolean`; `startDate`: `string`; `startTime`: `string`; `title`: `string`; `viewType`: `undefined`; \})[]

Defined in: [components/EventCalender/EventCalenderMocks.ts:7](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/EventCalender/EventCalenderMocks.ts#L7)
