[Admin Docs](/)

***

# Variable: MOCKS

> `const` **MOCKS**: (\{ `request`: \{ `query`: `DocumentNode`; `variable`: \{ `allDay`: `undefined`; `description`: `undefined`; `endTime`: `undefined`; `id`: `string`; `isPublic`: `undefined`; `isRegisterable`: `undefined`; `location`: `undefined`; `recurring`: `undefined`; `startTime`: `undefined`; `title`: `undefined`; \}; \}; `result`: \{ `data`: \{ `removeEvent`: \{ `_id`: `string`; \}; `updateEvent`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variable`: \{ `allDay`: `boolean`; `description`: `string`; `endTime`: `string`; `id`: `string`; `isPublic`: `boolean`; `isRegisterable`: `boolean`; `location`: `string`; `recurring`: `boolean`; `startTime`: `string`; `title`: `string`; \}; \}; `result`: \{ `data`: \{ `removeEvent`: `undefined`; `updateEvent`: \{ `_id`: `string`; \}; \}; \}; \})[]

Defined in: [components/EventCalender/EventCalenderMocks.ts:47](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/EventCalender/EventCalenderMocks.ts#L47)
