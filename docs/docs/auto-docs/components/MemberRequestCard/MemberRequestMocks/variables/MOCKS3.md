[Admin Docs](/)

***

# Variable: MOCKS3

> `const` **MOCKS3**: (\{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `id`: `string`; `userid`: `undefined`; \}; \}; `result`: \{ `data`: \{ `acceptMembershipRequest`: \{ `_id`: `string`; \}; `organizations`: `object`[]; `rejectMembershipRequest`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `id`: `undefined`; `userid`: `string`; \}; \}; `result`: \{ `data`: \{ `acceptMembershipRequest`: `undefined`; `organizations`: `object`[]; `rejectMembershipRequest`: \{ `_id`: `string`; \}; \}; \}; \})[]

Defined in: [components/MemberRequestCard/MemberRequestMocks.ts:82](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/MemberRequestCard/MemberRequestMocks.ts#L82)
