[Admin Docs](/)

***

# Variable: MOCKS

> `const` **MOCKS**: (\{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `id`: `string`; `userid`: `undefined`; \}; \}; `result`: \{ `data`: \{ `acceptMembershipRequest`: \{ `_id`: `string`; \}; `organizations`: `object`[]; `rejectMembershipRequest`: `undefined`; \}; \}; \} \| \{ `request`: \{ `query`: `DocumentNode`; `variables`: \{ `id`: `undefined`; `userid`: `string`; \}; \}; `result`: \{ `data`: \{ `acceptMembershipRequest`: `undefined`; `organizations`: `object`[]; `rejectMembershipRequest`: \{ `_id`: `string`; \}; \}; \}; \})[]

Defined in: [components/MemberRequestCard/MemberRequestMocks.ts:6](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/MemberRequestCard/MemberRequestMocks.ts#L6)
