[Admin Docs](/)

***

# Variable: mockEventData

> `const` **mockEventData**: `object`

Defined in: [components/MemberActivity/MemberActivityMocks.ts:3](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/MemberActivity/MemberActivityMocks.ts#L3)

## Type declaration

### event

> **event**: `object`

#### event.\_id

> **\_id**: `string` = `'event123'`

#### event.allDay

> **allDay**: `boolean` = `false`

#### event.attendees

> **attendees**: `object`[]

#### event.baseRecurringEvent

> **baseRecurringEvent**: `object`

#### event.baseRecurringEvent.\_id

> **\_id**: `string` = `'base123'`

#### event.description

> **description**: `string` = `'Test Description'`

#### event.endDate

> **endDate**: `string` = `'2023-01-02'`

#### event.endTime

> **endTime**: `string` = `'17:00'`

#### event.location

> **location**: `string` = `'Test Location'`

#### event.organization

> **organization**: `object`

#### event.organization.\_id

> **\_id**: `string` = `'org123'`

#### event.organization.members

> **members**: `object`[]

#### event.recurring

> **recurring**: `boolean` = `true`

#### event.startDate

> **startDate**: `string` = `'2023-01-01'`

#### event.startTime

> **startTime**: `string` = `'09:00'`

#### event.title

> **title**: `string` = `'Test Event'`
