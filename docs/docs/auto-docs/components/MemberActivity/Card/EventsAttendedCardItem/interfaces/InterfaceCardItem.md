[Admin Docs](/)

***

# Interface: InterfaceCardItem

Defined in: [components/MemberActivity/Card/EventsAttendedCardItem.tsx:42](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/MemberActivity/Card/EventsAttendedCardItem.tsx#L42)

## Properties

### creator?

> `optional` **creator**: `string`

Defined in: [components/MemberActivity/Card/EventsAttendedCardItem.tsx:46](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/MemberActivity/Card/EventsAttendedCardItem.tsx#L46)

***

### eventId?

> `optional` **eventId**: `string`

Defined in: [components/MemberActivity/Card/EventsAttendedCardItem.tsx:48](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/MemberActivity/Card/EventsAttendedCardItem.tsx#L48)

***

### location?

> `optional` **location**: `string`

Defined in: [components/MemberActivity/Card/EventsAttendedCardItem.tsx:47](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/MemberActivity/Card/EventsAttendedCardItem.tsx#L47)

***

### orgId?

> `optional` **orgId**: `string`

Defined in: [components/MemberActivity/Card/EventsAttendedCardItem.tsx:49](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/MemberActivity/Card/EventsAttendedCardItem.tsx#L49)

***

### startdate?

> `optional` **startdate**: `string`

Defined in: [components/MemberActivity/Card/EventsAttendedCardItem.tsx:45](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/MemberActivity/Card/EventsAttendedCardItem.tsx#L45)

***

### time?

> `optional` **time**: `string`

Defined in: [components/MemberActivity/Card/EventsAttendedCardItem.tsx:44](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/MemberActivity/Card/EventsAttendedCardItem.tsx#L44)

***

### title

> **title**: `string`

Defined in: [components/MemberActivity/Card/EventsAttendedCardItem.tsx:43](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/MemberActivity/Card/EventsAttendedCardItem.tsx#L43)
