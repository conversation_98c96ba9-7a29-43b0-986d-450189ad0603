[Admin Docs](/)

***

# Variable: props2

> `const` **props2**: `object`

Defined in: [components/AgendaItems/AgendaItemsMocks.ts:102](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/AgendaItems/AgendaItemsMocks.ts#L102)

## Type declaration

### agendaItemCategories

> **agendaItemCategories**: `any`[] = `[]`

### agendaItemConnection

> **agendaItemConnection**: `"Event"`

### agendaItemData

> **agendaItemData**: `any`[] = `[]`

### agendaItemRefetch

> **agendaItemRefetch**: `Mock`\<`Procedure`\>
