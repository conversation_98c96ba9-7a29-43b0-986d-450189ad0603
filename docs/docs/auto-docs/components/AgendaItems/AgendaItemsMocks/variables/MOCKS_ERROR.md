[Admin Docs](/)

***

# Variable: MOCKS\_ERROR

> `const` **MOCKS\_ERROR**: (\{ `error`: `Error`; `request`: \{ `query`: `any`; `variables`: \{ `input`: \{ `description`: `string`; `title`: `string`; \}; `removeAgendaItemId`: `undefined`; `updateAgendaItemId`: `string`; \}; \}; \} \| \{ `error`: `Error`; `request`: \{ `query`: `any`; `variables`: \{ `input`: `undefined`; `removeAgendaItemId`: `string`; `updateAgendaItemId`: `undefined`; \}; \}; \})[]

Defined in: [components/AgendaItems/AgendaItemsMocks.ts:199](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/AgendaItems/AgendaItemsMocks.ts#L199)
