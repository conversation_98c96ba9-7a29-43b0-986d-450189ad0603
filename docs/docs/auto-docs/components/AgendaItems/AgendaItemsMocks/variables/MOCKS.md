[Admin Docs](/)

***

# Variable: MOCKS

> `const` **MOCKS**: (\{ `request`: \{ `query`: `any`; `variables`: \{ `input`: \{ `description`: `string`; `title`: `string`; \}; `removeAgendaItemId`: `undefined`; `updateAgendaItemId`: `string`; \}; \}; `result`: \{ `data`: \{ `removeAgendaItem`: `undefined`; `updateAgendaItem`: \{ `_id`: `string`; \}; \}; \}; \} \| \{ `request`: \{ `query`: `any`; `variables`: \{ `input`: `undefined`; `removeAgendaItemId`: `string`; `updateAgendaItemId`: `undefined`; \}; \}; `result`: \{ `data`: \{ `removeAgendaItem`: \{ `_id`: `string`; \}; `updateAgendaItem`: `undefined`; \}; \}; \})[]

Defined in: [components/AgendaItems/AgendaItemsMocks.ts:109](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/AgendaItems/AgendaItemsMocks.ts#L109)
