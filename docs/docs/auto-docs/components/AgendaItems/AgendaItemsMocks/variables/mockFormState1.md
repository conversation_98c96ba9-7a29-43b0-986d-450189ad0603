[Admin Docs](/)

***

# Variable: mockFormState1

> `const` **mockFormState1**: `object`

Defined in: [components/AgendaItems/AgendaItemsMocks.ts:224](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/AgendaItems/AgendaItemsMocks.ts#L224)

## Type declaration

### agendaItemCategoryIds

> **agendaItemCategoryIds**: `string`[]

### agendaItemCategoryNames

> **agendaItemCategoryNames**: `string`[]

### attachments

> **attachments**: `string`[]

### createdBy

> **createdBy**: `object`

#### createdBy.firstName

> **firstName**: `string` = `'Test'`

#### createdBy.lastName

> **lastName**: `string` = `'User'`

### description

> **description**: `string` = `'Test Description'`

### duration

> **duration**: `string` = `'20'`

### title

> **title**: `string` = `'Test Title'`

### urls

> **urls**: `string`[]
