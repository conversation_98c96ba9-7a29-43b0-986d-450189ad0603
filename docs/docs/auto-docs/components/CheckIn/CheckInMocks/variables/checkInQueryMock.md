[Admin Docs](/)

***

# Variable: checkInQueryMock

> `const` **checkInQueryMock**: `object`[]

Defined in: [components/CheckIn/CheckInMocks.ts:34](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/CheckIn/CheckInMocks.ts#L34)

## Type declaration

### request

> **request**: `object`

#### request.query

> **query**: `DocumentNode` = `EVENT_CHECKINS`

#### request.variables

> **variables**: `object`

#### request.variables.id

> **id**: `string` = `'event123'`

### result

> **result**: `object`

#### result.data

> **data**: [`InterfaceAttendeeQueryResponse`](../../../../types/CheckIn/interface/interfaces/InterfaceAttendeeQueryResponse.md) = `checkInQueryData`
