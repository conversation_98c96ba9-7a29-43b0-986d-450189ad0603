[Admin Docs](/)

***

# Variable: mockData

> `const` **mockData**: `object`[]

Defined in: [components/EventStats/EventStatsMocks.ts:3](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/EventStats/EventStatsMocks.ts#L3)

## Type declaration

### request

> **request**: `object`

#### request.query

> **query**: `DocumentNode` = `EVENT_FEEDBACKS`

#### request.variables

> **variables**: `object`

#### request.variables.id

> **id**: `string` = `'eventStats123'`

### result

> **result**: `object`

#### result.data

> **data**: `object`

#### result.data.event

> **event**: `object`

#### result.data.event.\_id

> **\_id**: `string` = `'eventStats123'`

#### result.data.event.averageFeedbackScore

> **averageFeedbackScore**: `number` = `5`

#### result.data.event.feedback

> **feedback**: `object`[]
