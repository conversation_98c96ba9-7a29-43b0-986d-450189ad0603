[Admin Docs](/)

***

# Variable: dateConstants

> `const` **dateConstants**: `object`

Defined in: [components/Advertisements/core/AdvertisementRegister/AdvertisementRegisterMocks.ts:27](https://github.com/PalisadoesFoundation/talawa-admin/blob/main/src/components/Advertisements/core/AdvertisementRegister/AdvertisementRegisterMocks.ts#L27)

## Type declaration

### create

> **create**: `object`

#### create.endAtCalledWith

> **endAtCalledWith**: `string` = `'2030-02-01T00:00:00.000Z'`

#### create.endAtISO

> **endAtISO**: `string` = `'2030-02-01T18:30:00.000Z'`

#### create.endBeforeStartCalledWith

> **endBeforeStartCalledWith**: `string` = `'2010-02-01T00:00:00.000Z'`

#### create.endBeforeStartISO

> **endBeforeStartISO**: `string` = `'2010-02-01T18:30:00.000Z'`

#### create.endBeforeStartISOReceived

> **endBeforeStartISOReceived**: `string` = `'2010-01-31T18:30:00.000Z'`

#### create.endISOReceived

> **endISOReceived**: `string` = `'2030-01-31T18:30:00.000Z'`

#### create.startAtCalledWith

> **startAtCalledWith**: `string` = `'2024-12-31T00:00:00.000Z'`

#### create.startAtISO

> **startAtISO**: `string` = `'2024-12-31T18:30:00.000Z'`

#### create.startISOReceived

> **startISOReceived**: `string` = `'2024-12-30T18:30:00.000Z'`

### update

> **update**: `object`

#### update.endAtCalledWith

> **endAtCalledWith**: `string` = `'2040-02-01T00:00:00.000Z'`

#### update.endAtISO

> **endAtISO**: `string` = `'2040-02-01T18:30:00.000Z'`

#### update.endBeforeStartCalledWith

> **endBeforeStartCalledWith**: `string` = `'2010-02-01T00:00:00.000Z'`

#### update.endBeforeStartISO

> **endBeforeStartISO**: `string` = `'2010-02-01T18:30:00.000Z'`

#### update.endBeforeStartISOReceived

> **endBeforeStartISOReceived**: `string` = `'2010-01-31T18:30:00.000Z'`

#### update.endISOReceived

> **endISOReceived**: `string` = `'2040-01-31T18:30:00.000Z'`

#### update.startAtCalledWith

> **startAtCalledWith**: `string` = `'2020-12-31T00:00:00.000Z'`

#### update.startAtISO

> **startAtISO**: `string` = `'2020-12-31T18:30:00.000Z'`

#### update.startISOReceived

> **startISOReceived**: `string` = `'2024-12-30T18:30:00.000Z'`
